/**
 * Executor service types and interfaces
 */

export interface ExecutorInfo {
  id: string;
  region: string;
  modelType: string;
  publicUrl: string;
  status: 'active' | 'inactive' | 'maintenance';
  lastHeartbeat: number;
  maxConcurrentRequests: number;
  currentRequests: number;
  supportedModels: string[];
}

export interface ExecutorConfig {
  region: string;
  modelType: string;
  publicUrl: string;
  maxConcurrentRequests: number;
}

export interface HeartbeatInfo {
  executorId: string;
  timestamp: number;
  currentRequests: number;
}

/**
 * Executor registration data stored in etcd (according to design document)
 */
export interface ExecutorRegistrationData {
  url: string;                    // Executor 对外服务 URL
  latency: number;               // 最近 1 分钟的模型调用平均耗时（ms）
  load: number;                  // 当前负载（如并发数比例）
  started_at: number;            // 启动时间 (timestamp)
  last_heartbeat: number;        // 最后一次心跳时间 (timestamp)
}

/**
 * Latency tracking for executor performance metrics
 */
export interface LatencyTracker {
  addLatency(latency: number): void;
  getAverageLatency(): number;
  reset(): void;
}
