/**
 * Executor service constants
 */

export const EXECUTOR_CONSTANTS = {
  // Heartbeat configuration (according to design document)
  HEARTBEAT_INTERVAL_MS: 30000, // 30 seconds
  LEASE_TTL_SECONDS: 60, // 60 seconds (TTL should be 60s, heartbeat every 30s)

  // Registration delays
  REGISTRATION_DELAY_MS: 2000, // 2 seconds

  // Default values
  DEFAULT_REGION: 'default',
  DEFAULT_MODEL_TYPE: 'openai',
  DEFAULT_PUBLIC_URL: 'http://localhost:3000',
  DEFAULT_MAX_CONCURRENT_REQUESTS: 10,

  // Key lock configuration
  KEY_LOCK_TIMEOUT_MS: 5 * 60 * 1000, // 5 minutes

  // Lease renewal configuration (according to design document)
  LEASE_RENEWAL_INTERVAL_MS: 30000, // 30 seconds
} as const;

export const SUPPORTED_MODELS = {
  openai: [
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k',
    'gpt-4',
    'gpt-4-32k',
    'gpt-4-turbo',
    'gpt-4-turbo-preview',
    'gpt-4o',
    'gpt-4o-mini',
  ],
} as const;


