import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

@Injectable()
export class ConfigValidationService {
  private readonly logger = new Logger(ConfigValidationService.name);

  constructor(private configService: ConfigService) {}

  validateConfiguration(): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required configurations
    this.validateRequiredConfig(errors);

    // Validate etcd configuration
    this.validateEtcdConfig(errors, warnings);

    // Validate provider configurations
    this.validateProviderConfigs(warnings);

    const result: ConfigValidationResult = {
      valid: errors.length === 0,
      errors,
      warnings,
    };

    this.logValidationResult(result);
    return result;
  }

  private validateRequiredConfig(errors: string[]): void {
    const requiredConfigs = ['executor.modelType', 'etcd.endpoints'];

    for (const config of requiredConfigs) {
      const value = this.configService.get<string>(config);
      if (!value) {
        errors.push(`Required configuration missing: ${config}`);
      }
    }
  }

  // Removed validateExecutorConfig as we simplified the configuration

  private validateEtcdConfig(errors: string[], warnings: string[]): void {
    const endpoints = this.configService.get<string[]>('etcd.endpoints');

    if (endpoints) {
      for (const endpoint of endpoints) {
        if (!this.isValidEndpoint(endpoint)) {
          errors.push(`Invalid etcd endpoint format: ${endpoint}`);
        }
      }
    }

    const username = this.configService.get<string>('etcd.username');
    const password = this.configService.get<string>('etcd.password');

    if (username && !password) {
      warnings.push('etcd username provided but password is missing');
    }
    if (password && !username) {
      warnings.push('etcd password provided but username is missing');
    }
  }

  private validateProviderConfigs(warnings: string[]): void {
    const modelType = this.configService.get<string>('executor.modelType');

    // Validate OpenAI config
    if (modelType === 'openai') {
      const baseUrl = this.configService.get<string>('openai.baseUrl');
      if (baseUrl && !this.isValidUrl(baseUrl)) {
        warnings.push(`Invalid OpenAI base URL: ${baseUrl}`);
      }
    }

    // Validate Anthropic config
    if (modelType === 'anthropic') {
      const baseUrl = this.configService.get<string>('anthropic.baseUrl');
      if (baseUrl && !this.isValidUrl(baseUrl)) {
        warnings.push(`Invalid Anthropic base URL: ${baseUrl}`);
      }
    }

    // Validate DeepSeek config
    if (modelType === 'deepseek') {
      const baseUrl = this.configService.get<string>('deepseek.baseUrl');
      if (baseUrl && !this.isValidUrl(baseUrl)) {
        warnings.push(`Invalid DeepSeek base URL: ${baseUrl}`);
      }
    }
  }

  // Removed validateHeartbeatConfig as we no longer use heartbeat functionality

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private isValidEndpoint(endpoint: string): boolean {
    // Basic validation for host:port format
    const pattern = /^[a-zA-Z0-9.-]+:\d+$/;
    return pattern.test(endpoint);
  }

  private logValidationResult(result: ConfigValidationResult): void {
    if (result.valid) {
      this.logger.log('✅ Configuration validation passed');
    } else {
      this.logger.error('❌ Configuration validation failed');
      result.errors.forEach((error) => this.logger.error(`  - ${error}`));
    }

    if (result.warnings.length > 0) {
      this.logger.warn('⚠️  Configuration warnings:');
      result.warnings.forEach((warning) => this.logger.warn(`  - ${warning}`));
    }
  }

  getConfigSummary(): Record<string, any> {
    return {
      executor: {
        modelType: this.configService.get<string>('executor.modelType'),
        keyPoolSize: this.configService.get<number>('executor.keyPoolSize'),
      },
      etcd: {
        endpoints: this.configService.get<string[]>('etcd.endpoints'),
        hasAuth: !!(
          this.configService.get<string>('etcd.username') &&
          this.configService.get<string>('etcd.password')
        ),
      },
      providers: {
        openai: {
          baseUrl: this.configService.get<string>('openai.baseUrl'),
          defaultModel: this.configService.get<string>('openai.defaultModel'),
        },
      },
    };
  }
}
