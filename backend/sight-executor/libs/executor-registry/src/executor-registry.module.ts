import { Module } from '@nestjs/common';
import { ExecutorRegistryService } from './executor-registry.service';
// import { PublicKeyRegistryService } from './public-key-registry.service'; // Temporarily disabled
import { EtcdModule } from '@app/etcd';
import { SecretManagerModule } from '@app/secret-manager';

@Module({
  imports: [EtcdModule, SecretManagerModule],
  providers: [ExecutorRegistryService], // PublicKeyRegistryService temporarily removed
  exports: [ExecutorRegistryService], // PublicKeyRegistryService temporarily removed
})
export class ExecutorRegistryModule {}
