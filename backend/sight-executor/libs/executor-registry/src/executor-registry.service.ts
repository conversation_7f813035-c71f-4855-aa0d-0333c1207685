import { Injectable, Logger, OnApplicationBootstrap, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EtcdService } from '@app/etcd';
import { ExecutorInfo, ExecutorConfig, ExecutorRegistrationData, LatencyTracker } from '@app/types';
import { EXECUTOR_CONSTANTS, SUPPORTED_MODELS, generateExecutorPath, getSupportedModels } from '@app/config';
import { v4 as uuidv4 } from 'uuid';
import { Etcd3 } from 'etcd3';

// Type definition for etcd lease
interface EtcdLease {
  revoke(): Promise<void>;
  keepaliveOnce(): Promise<unknown>;
}

// Simple latency tracker implementation
class SimpleLatencyTracker implements LatencyTracker {
  private latencies: number[] = [];
  private readonly maxSamples = 60; // Keep last 60 samples (1 minute if 1 sample per second)

  addLatency(latency: number): void {
    this.latencies.push(latency);
    if (this.latencies.length > this.maxSamples) {
      this.latencies.shift();
    }
  }

  getAverageLatency(): number {
    if (this.latencies.length === 0) return 0;
    const sum = this.latencies.reduce((acc, lat) => acc + lat, 0);
    return Math.round(sum / this.latencies.length);
  }

  reset(): void {
    this.latencies = [];
  }
}

@Injectable()
export class ExecutorRegistryService implements OnApplicationBootstrap, OnModuleDestroy {
  private readonly logger = new Logger(ExecutorRegistryService.name);
  private executorInfo: ExecutorInfo | null = null;
  private executorLease: EtcdLease | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private etcdClient: Etcd3 | null = null;
  private latencyTracker = new SimpleLatencyTracker();
  private startedAt: number = Date.now();

  constructor(
    private readonly etcdService: EtcdService,
    private readonly configService: ConfigService,
  ) {
    this.logger.log('ExecutorRegistryService constructor called');
  }

  async onApplicationBootstrap(): Promise<void> {
    try {
      this.logger.log('ExecutorRegistryService starting...');

      // Get etcd client from etcd service
      this.etcdClient = this.etcdService.getEtcdClient();

      if (!this.etcdClient) {
        throw new Error('Failed to get etcd client');
      }

      // Wait a bit for other services to initialize, then register
      setTimeout(async () => {
        try {
          await this.registerExecutor();
        } catch (error) {
          this.logger.error(
            `Failed to register executor: ${error instanceof Error ? error.message : String(error)}`,
          );
        }
      }, 2000);

      this.logger.log('ExecutorRegistryService initialized');
    } catch (error) {
      this.logger.error(`Failed to initialize ExecutorRegistryService: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async onModuleDestroy(): Promise<void> {
    if (this.executorInfo) {
      await this.unregisterExecutor();
    }
  }

  private async registerExecutor(): Promise<void> {
    try {
      // Use consistent executor ID from config (matches EncryptionService)
      const executorId = this.configService.get<string>('executor.id', 'executor-001');

      // Get configuration
      const region = this.configService.get<string>('executor.region', 'default');
      const modelType = this.configService.get<string>('executor.modelType', 'openai');
      const publicUrl = this.configService.get<string>('executor.publicUrl', 'http://localhost:3000');
      const maxConcurrentRequests = this.configService.get<number>('executor.maxConcurrentRequests', 10);

      // Create executor info
      this.executorInfo = {
        id: executorId,
        region,
        modelType,
        publicUrl,
        status: 'active',
        lastHeartbeat: Date.now(),
        maxConcurrentRequests,
        currentRequests: 0,
        supportedModels: getSupportedModels(modelType),
      };

      // Register with etcd directly
      await this.registerExecutorWithEtcd(this.executorInfo);

      this.logger.log(`Executor registered successfully: ${executorId}`);
      this.logger.log(`Region: ${region}, Model Type: ${modelType}`);
      this.logger.log(`Public URL: ${publicUrl}`);
    } catch (error) {
      this.logger.error(
        `Failed to register executor: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  private async registerExecutorWithEtcd(executorInfo: ExecutorInfo): Promise<void> {
    if (!this.etcdClient) {
      throw new Error('etcd client is not initialized');
    }

    const executorPath = generateExecutorPath(
      executorInfo.region,
      executorInfo.modelType,
      executorInfo.id,
    );

    try {
      // Create a lease for the executor registration (60 seconds TTL as per design doc)
      const lease = this.etcdClient.lease(60);
      const leaseId = await lease.grant();
      this.executorLease = lease;

      // Create registration data with both design document format and Gateway-compatible format
      const registrationData = {
        // Design document format (for backward compatibility)
        url: executorInfo.publicUrl,
        latency: this.latencyTracker.getAverageLatency(),
        load: this.calculateLoad(),
        started_at: this.startedAt,
        last_heartbeat: Date.now(),

        // Gateway-compatible format (for ExecutorDiscoveryService)
        id: executorInfo.id,
        region: executorInfo.region,
        modelType: executorInfo.modelType,
        status: executorInfo.status,
        lastHeartbeat: Date.now(),
        maxConcurrentRequests: executorInfo.maxConcurrentRequests,
        currentRequests: executorInfo.currentRequests,
        supportedModels: executorInfo.supportedModels,
        publicUrl: executorInfo.publicUrl,
      };

      // Register the executor with the lease
      await this.etcdClient
        .put(executorPath)
        .value(JSON.stringify(registrationData))
        .lease(leaseId)
        .exec();

      // Start heartbeat to keep the lease alive
      this.startHeartbeat(executorPath, executorInfo);

      this.logger.log(`Registered executor: ${executorPath}`);
    } catch (error) {
      this.logger.error(
        `Failed to register executor: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  private startHeartbeat(executorPath: string, executorInfo: ExecutorInfo): void {
    // Send heartbeat every 30 seconds (lease TTL is 60 seconds)
    this.heartbeatInterval = setInterval(async () => {
      try {
        if (this.executorLease && this.etcdClient) {
          await this.executorLease.keepaliveOnce();

          // Update heartbeat timestamp
          executorInfo.lastHeartbeat = Date.now();

          // Create updated registration data with both formats
          const registrationData = {
            // Design document format (for backward compatibility)
            url: executorInfo.publicUrl,
            latency: this.latencyTracker.getAverageLatency(),
            load: this.calculateLoad(),
            started_at: this.startedAt,
            last_heartbeat: Date.now(),

            // Gateway-compatible format (for ExecutorDiscoveryService)
            id: executorInfo.id,
            region: executorInfo.region,
            modelType: executorInfo.modelType,
            status: executorInfo.status,
            lastHeartbeat: Date.now(),
            maxConcurrentRequests: executorInfo.maxConcurrentRequests,
            currentRequests: executorInfo.currentRequests,
            supportedModels: executorInfo.supportedModels,
            publicUrl: executorInfo.publicUrl,
          };

          // Update executor info in etcd
          await this.etcdClient.put(executorPath).value(JSON.stringify(registrationData)).exec();

          this.logger.debug(`Heartbeat sent for executor: ${executorPath}`);
        }
      } catch (error) {
        this.logger.error(
          `Heartbeat failed for executor ${executorPath}: ${error instanceof Error ? error.message : String(error)}`,
        );
        // Try to re-register if heartbeat fails
        this.registerExecutorWithEtcd(executorInfo).catch((regError) => {
          this.logger.error(
            `Failed to re-register executor: ${regError instanceof Error ? regError.message : String(regError)}`,
          );
        });
      }
    }, 30000); // 30 seconds as per design doc
  }

  private async unregisterExecutor(): Promise<void> {
    if (!this.executorInfo) {
      return;
    }

    try {
      // Stop heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }

      // Revoke lease to immediately remove executor
      if (this.executorLease) {
        await this.executorLease.revoke();
        this.executorLease = null;
      }

      this.logger.log(`Executor unregistered: ${this.executorInfo.id}`);
      this.executorInfo = null;
    } catch (error) {
      this.logger.error(
        `Failed to unregister executor: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Update current request count
   */
  updateCurrentRequests(count: number): void {
    if (this.executorInfo) {
      this.executorInfo.currentRequests = count;
    }
  }

  /**
   * Get executor info
   */
  getExecutorInfo(): ExecutorInfo | null {
    return this.executorInfo;
  }

  /**
   * Calculate current load as a ratio (0.0 to 1.0)
   */
  private calculateLoad(): number {
    if (!this.executorInfo) return 0;
    return this.executorInfo.currentRequests / this.executorInfo.maxConcurrentRequests;
  }

  /**
   * Record latency for a request
   */
  recordLatency(latency: number): void {
    this.latencyTracker.addLatency(latency);
  }

  /**
   * Get average latency
   */
  getAverageLatency(): number {
    return this.latencyTracker.getAverageLatency();
  }

  /**
   * Get current load
   */
  getCurrentLoad(): number {
    return this.calculateLoad();
  }
}
