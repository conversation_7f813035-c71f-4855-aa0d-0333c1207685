import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EtcdService } from '@app/etcd';
import { SecretManagerService } from '@app/secret-manager';
import * as nacl from 'tweetnacl';

export interface PublicKeyRegistration {
  keyId: string;                  // executor 公钥标识
  publicKey: string;             // base64(X25519 pub) - base64 编码公钥
  provider: 'openai' | 'claude' | 'anthropic' | 'google' | 'cohere'; // 模型提供商
  region: string;                // 区域
  executorUrl?: string;          // Executor URL (optional)
  registeredAt: string;          // 注册时间
  lastHeartbeat?: string;        // 最后心跳时间
  status: 'active' | 'inactive'; // 状态
}

@Injectable()
export class PublicKeyRegistryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PublicKeyRegistryService.name);
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private keyPair: nacl.BoxKeyPair | null = null;
  private registration: PublicKeyRegistration | null = null;

  private readonly executorId: string;
  private readonly provider: string;
  private readonly region: string;
  private readonly executorUrl: string;

  constructor(
    private etcdService: EtcdService,
    private secretManagerService: SecretManagerService,
    private configService: ConfigService,
  ) {
    this.executorId = this.configService.get<string>('executor.id', 'executor-' + Date.now());
    this.provider = this.configService.get<string>('executor.modelType', 'openai');
    this.region = this.configService.get<string>('executor.region', 'default');
    this.executorUrl = this.configService.get<string>('executor.publicUrl', 'http://localhost:3000');
  }

  async onModuleInit() {
    try {
      await this.initializeKeyPair();
      await this.registerPublicKey();
      this.startHeartbeat();
    } catch (error) {
      this.logger.error(`Failed to initialize public key registry: ${error}`);
      throw error;
    }
  }

  async onModuleDestroy() {
    await this.cleanup();
  }

  /**
   * Initialize key pair from Secret Manager or environment
   */
  private async initializeKeyPair(): Promise<void> {
    try {
      this.logger.log('Initializing executor key pair...');

      // Load private key from Secret Manager or environment
      const privateKeyBase64 = await this.secretManagerService.loadPrivateKey();

      if (!privateKeyBase64) {
        throw new Error('No private key available from Secret Manager or environment variables');
      }

      const privateKeyBuffer = Buffer.from(privateKeyBase64, 'base64');

      if (privateKeyBuffer.length !== 32) {
        throw new Error('Private key must be 32 bytes for X25519');
      }

      // Create key pair from private key
      this.keyPair = nacl.box.keyPair.fromSecretKey(privateKeyBuffer);

      this.logger.log(`Successfully initialized key pair for executor: ${this.executorId}`);
    } catch (error) {
      this.logger.error(`Failed to initialize key pair: ${error}`);
      throw error;
    }
  }

  /**
   * Register public key in etcd
   * Path: /keys/public/{provider}/{region}/{keyId}
   */
  async registerPublicKey(): Promise<void> {
    try {
      if (!this.keyPair) {
        throw new Error('Key pair not initialized');
      }

      const keyId = `${this.executorId}-${Date.now()}`;
      const publicKeyBase64 = Buffer.from(this.keyPair.publicKey).toString('base64');

      this.registration = {
        keyId,
        publicKey: publicKeyBase64,
        provider: this.provider as any,
        region: this.region,
        executorUrl: this.executorUrl,
        registeredAt: new Date().toISOString(),
        status: 'active',
      };

      const keyPath = `/keys/public/${this.provider}/${this.region}/${keyId}`;
      const etcdClient = this.etcdService.getEtcdClient();

      if (!etcdClient) {
        throw new Error('etcd client not available');
      }

      await etcdClient.put(keyPath).value(JSON.stringify(this.registration));

      this.logger.log(
        `Registered public key: ${keyId} at path: ${keyPath}`,
      );
    } catch (error) {
      this.logger.error(`Failed to register public key: ${error}`);
      throw error;
    }
  }

  /**
   * Start heartbeat to keep public key registration alive
   */
  private startHeartbeat(): void {
    // Send heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(async () => {
      try {
        await this.sendHeartbeat();
      } catch (error) {
        this.logger.error(`Heartbeat failed: ${error}`);
      }
    }, 30000); // 30 seconds

    this.logger.log('Started public key heartbeat');
  }

  /**
   * Send heartbeat to update lastHeartbeat timestamp
   */
  private async sendHeartbeat(): Promise<void> {
    try {
      if (!this.registration) {
        this.logger.warn('No registration found for heartbeat');
        return;
      }

      // Update heartbeat timestamp
      this.registration.lastHeartbeat = new Date().toISOString();

      const keyPath = `/keys/public/${this.provider}/${this.region}/${this.registration.keyId}`;
      const etcdClient = this.etcdService.getEtcdClient();

      if (!etcdClient) {
        throw new Error('etcd client not available');
      }

      await etcdClient.put(keyPath).value(JSON.stringify(this.registration));

      this.logger.debug(`Sent heartbeat for key: ${this.registration.keyId}`);
    } catch (error) {
      this.logger.error(`Failed to send heartbeat: ${error}`);
      throw error;
    }
  }

  /**
   * Update public key status
   */
  async updateStatus(status: 'active' | 'inactive'): Promise<void> {
    try {
      if (!this.registration) {
        throw new Error('No registration found');
      }

      this.registration.status = status;
      this.registration.lastHeartbeat = new Date().toISOString();

      const keyPath = `/keys/public/${this.provider}/${this.region}/${this.registration.keyId}`;
      const etcdClient = this.etcdService.getEtcdClient();

      if (!etcdClient) {
        throw new Error('etcd client not available');
      }

      await etcdClient.put(keyPath).value(JSON.stringify(this.registration));

      this.logger.log(`Updated public key status to: ${status}`);
    } catch (error) {
      this.logger.error(`Failed to update status: ${error}`);
      throw error;
    }
  }

  /**
   * Get current registration info
   */
  getRegistration(): PublicKeyRegistration | null {
    return this.registration;
  }

  /**
   * Get public key for external use
   */
  getPublicKey(): string | null {
    if (!this.keyPair) {
      return null;
    }
    return Buffer.from(this.keyPair.publicKey).toString('base64');
  }

  /**
   * Get private key for decryption (internal use only)
   */
  getPrivateKey(): Uint8Array | null {
    if (!this.keyPair) {
      return null;
    }
    return this.keyPair.secretKey;
  }

  /**
   * Get key pair for encryption/decryption operations
   */
  getKeyPair(): nacl.BoxKeyPair | null {
    return this.keyPair;
  }

  /**
   * Cleanup: remove public key registration and stop heartbeat
   */
  private async cleanup(): Promise<void> {
    try {
      // Stop heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
        this.logger.log('Stopped public key heartbeat');
      }

      // Remove public key registration
      if (this.registration) {
        const keyPath = `/keys/public/${this.provider}/${this.region}/${this.registration.keyId}`;
        const etcdClient = this.etcdService.getEtcdClient();

        if (etcdClient) {
          await etcdClient.delete().key(keyPath);
          this.logger.log(`Removed public key registration: ${this.registration.keyId}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup public key registry: ${error}`);
    }
  }

  /**
   * Force re-registration (useful for recovery)
   */
  async reregister(): Promise<void> {
    try {
      this.logger.log('Force re-registering public key...');
      
      // Stop current heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }

      // Re-register
      await this.registerPublicKey();
      this.startHeartbeat();

      this.logger.log('Successfully re-registered public key');
    } catch (error) {
      this.logger.error(`Failed to re-register public key: ${error}`);
      throw error;
    }
  }

  /**
   * Get registration statistics
   */
  getRegistrationStats(): {
    executorId: string;
    keyId: string | null;
    provider: string;
    region: string;
    status: string | null;
    registeredAt: string | null;
    lastHeartbeat: string | null;
    heartbeatActive: boolean;
  } {
    return {
      executorId: this.executorId,
      keyId: this.registration?.keyId || null,
      provider: this.provider,
      region: this.region,
      status: this.registration?.status || null,
      registeredAt: this.registration?.registeredAt || null,
      lastHeartbeat: this.registration?.lastHeartbeat || null,
      heartbeatActive: this.heartbeatInterval !== null,
    };
  }
}
