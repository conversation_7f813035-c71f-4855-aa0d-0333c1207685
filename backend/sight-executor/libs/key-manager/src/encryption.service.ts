import { Injectable, Logger, OnModuleInit, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EtcdService } from '@app/etcd';
import { SecretManagerService } from '@app/secret-manager';
import { EncryptedApiKeyData, PublicKeyRegistration } from '@app/types';
import { KeyStatusManagerService } from './key-status-manager.service';
import * as nacl from 'tweetnacl';
import { chacha20poly1305 } from '@noble/ciphers/chacha';

/**
 * Encryption Service
 * Handles X25519 + ChaCha20-Poly1305 encryption/decryption according to design document
 */
@Injectable()
export class EncryptionService implements OnModuleInit {
  private readonly logger = new Logger(EncryptionService.name);
  private executorKeyPair: nacl.BoxKeyPair | null = null;
  private readonly provider: string;
  private readonly region: string;
  private readonly executorId: string;

  constructor(
    private configService: ConfigService,
    private etcdService: EtcdService,
    private secretManagerService: SecretManagerService,
    @Inject(forwardRef(() => KeyStatusManagerService))
    private keyStatusManager: KeyStatusManagerService,
  ) {
    this.provider = this.configService.get<string>('executor.modelType', 'openai');
    this.region = this.configService.get<string>('executor.region', 'default');
    // Use a consistent executor ID that matches ExecutorRegistryService
    this.executorId = this.configService.get<string>('executor.id', 'executor-001');
  }

  async onModuleInit() {
    await this.initializeKeyPair();
    await this.registerPublicKey();

    // Start watching for new encrypted API keys
    this.startApiKeyWatcher();

    // Start periodic check for waiting-to-verify keys
    this.startPeriodicKeyCheck();

    this.logger.log('Encryption service initialized');
  }

  /**
   * Initialize executor key pair
   * Priority: GCP Secret Manager -> Environment Variable -> Generate New
   */
  private async initializeKeyPair(): Promise<void> {
    try {
      this.logger.log('Initializing executor key pair...');

      // Try to load from GCP Secret Manager first
      try {
        const privateKeyBase64 = await this.secretManagerService.loadPrivateKey();

        if (privateKeyBase64) {
          const privateKey = this.base64ToUint8Array(privateKeyBase64);

          // Validate private key length (X25519 private keys are 32 bytes)
          if (privateKey.length !== 32) {
            throw new Error(`Invalid private key length: ${privateKey.length}, expected 32 bytes`);
          }

          const publicKey = nacl.scalarMult.base(privateKey);

          this.executorKeyPair = {
            secretKey: privateKey,
            publicKey: publicKey
          };

          this.logger.log('✅ Loaded executor key pair from GCP Secret Manager');
          this.logger.log(`   Public key: ${this.uint8ArrayToBase64(this.executorKeyPair.publicKey)}`);
          return;
        }
      } catch (secretError) {
        this.logger.warn(`Failed to load from GCP Secret Manager: ${secretError instanceof Error ? secretError.message : String(secretError)}`);
        this.logger.log('Falling back to environment variable...');
      }

      // Fallback to environment variable
      const privateKeyBase64 = this.configService.get<string>('EXECUTOR_PRIVATE_KEY');

      if (privateKeyBase64) {
        try {
          const privateKey = this.base64ToUint8Array(privateKeyBase64);

          // Validate private key length
          if (privateKey.length !== 32) {
            throw new Error(`Invalid private key length: ${privateKey.length}, expected 32 bytes`);
          }

          const publicKey = nacl.scalarMult.base(privateKey);

          this.executorKeyPair = {
            secretKey: privateKey,
            publicKey: publicKey
          };

          this.logger.log('✅ Loaded executor key pair from environment variable');
          this.logger.log(`   Public key: ${this.uint8ArrayToBase64(this.executorKeyPair.publicKey)}`);
          return;
        } catch (error) {
          this.logger.warn(`Failed to load private key from environment: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // Last resort: generate new key pair (development only)
      this.logger.warn('⚠️  No persistent key found, generating new key pair (DEVELOPMENT MODE ONLY)');
      this.executorKeyPair = nacl.box.keyPair();

      this.logger.warn('🔑 Generated new executor key pair:');
      this.logger.warn(`   Private key: ${this.uint8ArrayToBase64(this.executorKeyPair.secretKey)}`);
      this.logger.warn(`   Public key: ${this.uint8ArrayToBase64(this.executorKeyPair.publicKey)}`);
      this.logger.warn('⚠️  This key pair will be lost on restart! Use GCP Secret Manager for production.');

    } catch (error) {
      this.logger.error(`Failed to initialize key pair: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Register public key in etcd according to design document
   * Path: /keys/public/{provider}/{region}/{keyId}
   */
  private async registerPublicKey(): Promise<void> {
    if (!this.executorKeyPair) {
      throw new Error('Executor key pair not initialized');
    }

    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const keyPath = `/keys/public/${this.provider}/${this.region}/${this.executorId}`;
      
      const publicKeyRegistration: PublicKeyRegistration = {
        keyId: this.executorId,
        publicKey: this.uint8ArrayToBase64(this.executorKeyPair.publicKey),
        provider: this.provider as 'openai' | 'claude',
        region: this.region,
        status: 'active',
        registeredAt: new Date().toISOString(),
        lastHeartbeat: new Date().toISOString(),
        executorUrl: `http://localhost:${this.configService.get<number>('port', 3001)}`
      };

      await etcdClient.put(keyPath).value(JSON.stringify(publicKeyRegistration));

      this.logger.log(`Registered public key at path: ${keyPath}`);

      // Start heartbeat to keep the key active
      this.startHeartbeat();
    } catch (error) {
      this.logger.error(`Failed to register public key: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Start heartbeat to keep public key active
   */
  private startHeartbeat(): void {
    // Send heartbeat every 2 minutes (well within the 5-minute timeout)
    setInterval(async () => {
      try {
        await this.sendHeartbeat();
      } catch (error) {
        this.logger.error(`Failed to send heartbeat: ${error instanceof Error ? error.message : String(error)}`);
      }
    }, 2 * 60 * 1000); // 2 minutes
  }

  /**
   * Start watching for new encrypted API keys
   */
  private async startApiKeyWatcher(): Promise<void> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        this.logger.warn('etcd client not available, API key watcher not started');
        return;
      }

      const watchPrefix = `/api-keys/${this.provider}/${this.region}/`;
      this.logger.log(`🔍 Starting API key watcher for prefix: ${watchPrefix}`);

      // Watch for new API keys
      const watcher = await etcdClient.watch().prefix(watchPrefix).create();

      watcher.on('put', async (event: any) => {
        try {
          const keyPath = event.key.toString();
          const keyData = JSON.parse(event.value.toString());

          this.logger.log(`📥 New API key detected: ${keyPath}`);

          // Check if this key is for our executor and needs verification
          if (keyData.keyId === this.executorId && keyData.status === 'waiting-to-verify') {
            this.logger.log(`🔐 Attempting to decrypt and verify new API key: ${keyData.keyId}`);

            // Try to decrypt the new key
            try {
              const decryptedKey = await this.decryptApiKey(keyData);
              if (decryptedKey) {
                // Mark as active after successful decryption
                await this.markKeyAsActive(keyData);
                this.logger.log(`✅ Successfully verified and activated new API key: ${keyData.keyId}`);
              }
            } catch (decryptError) {
              this.logger.error(`❌ Failed to decrypt new API key: ${decryptError instanceof Error ? decryptError.message : String(decryptError)}`);
            }
          }
        } catch (error) {
          this.logger.error(`Error processing API key watch event: ${error instanceof Error ? error.message : String(error)}`);
        }
      });

      watcher.on('error', (error: any) => {
        this.logger.error(`API key watcher error: ${error instanceof Error ? error.message : String(error)}`);
        // Restart watcher after a delay
        setTimeout(() => {
          this.logger.log('Restarting API key watcher...');
          this.startApiKeyWatcher();
        }, 5000);
      });

      this.logger.log('✅ API key watcher started successfully');
    } catch (error) {
      this.logger.error(`Failed to start API key watcher: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Start periodic check for waiting-to-verify keys
   */
  private startPeriodicKeyCheck(): void {
    // Check every 30 seconds for waiting-to-verify keys
    setInterval(async () => {
      try {
        await this.checkAndVerifyWaitingKeys();
      } catch (error) {
        this.logger.debug(`Periodic key check error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }, 30 * 1000); // 30 seconds
  }

  /**
   * Check for waiting-to-verify keys and try to decrypt them
   */
  private async checkAndVerifyWaitingKeys(): Promise<void> {
    try {
      const encryptedKeys = await this.getEncryptedApiKeys();
      const waitingKeys = encryptedKeys.filter(key => key.status === 'waiting-to-verify');

      if (waitingKeys.length > 0) {
        this.logger.debug(`🔍 Found ${waitingKeys.length} waiting-to-verify keys, attempting verification...`);

        for (const keyData of waitingKeys) {
          try {
            const decryptedKey = await this.decryptApiKey(keyData);
            if (decryptedKey) {
              await this.markKeyAsActive(keyData);
              this.logger.log(`✅ Verified and activated waiting key: ${keyData.keyId}`);
            }
          } catch (decryptError) {
            this.logger.debug(`Failed to decrypt waiting key ${keyData.keyId}: ${decryptError instanceof Error ? decryptError.message : String(decryptError)}`);
          }
        }
      }
    } catch (error) {
      this.logger.debug(`Error in periodic key check: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Send heartbeat to update lastHeartbeat timestamp
   */
  private async sendHeartbeat(): Promise<void> {
    if (!this.executorKeyPair) {
      return;
    }

    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const keyPath = `/keys/public/${this.provider}/${this.region}/${this.executorId}`;

      // Get current registration data
      const currentData = await etcdClient.get(keyPath);
      if (currentData) {
        const registration = JSON.parse(currentData as string) as PublicKeyRegistration;
        registration.lastHeartbeat = new Date().toISOString();

        await etcdClient.put(keyPath).value(JSON.stringify(registration));
        this.logger.debug(`Sent heartbeat for key: ${this.executorId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send heartbeat: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Decrypt API key using X25519 + ChaCha20-Poly1305
   * According to design document decryption flow
   * Now includes caching to avoid repeated decryption
   */
  async decryptApiKey(encryptedData: EncryptedApiKeyData): Promise<string> {
    if (!this.executorKeyPair) {
      throw new Error('Executor key pair not initialized');
    }

    // Check cache first
    const cacheKey = `${encryptedData.keyId}-${encryptedData.nonce}`;
    const cachedKey = this.keyStatusManager?.getCachedKey(cacheKey);
    if (cachedKey) {
      this.logger.debug(`Using cached decrypted key for keyId: ${encryptedData.keyId}`);
      return cachedKey;
    }

    try {
      // Step 1: Decode all components from base64
      const ciphertext = this.base64ToUint8Array(encryptedData.encryptedKey);
      const nonce = this.base64ToUint8Array(encryptedData.nonce);
      const tag = this.base64ToUint8Array(encryptedData.tag);
      const ephemeralPubKey = this.base64ToUint8Array(encryptedData.ephemeralPubKey);

      // Step 2: Derive shared secret using X25519 ECDH
      const sharedSecret = nacl.scalarMult(this.executorKeyPair.secretKey, ephemeralPubKey);

      // Step 3: Decrypt using ChaCha20-Poly1305
      const aead = chacha20poly1305(sharedSecret, nonce);
      const encryptedPayload = new Uint8Array(ciphertext.length + tag.length);
      encryptedPayload.set(ciphertext);
      encryptedPayload.set(tag, ciphertext.length);
      const plaintext = aead.decrypt(encryptedPayload);

      // Step 4: Convert to string
      const decoder = new TextDecoder();
      const apiKey = decoder.decode(plaintext);

      // Cache the decrypted key for future use
      if (this.keyStatusManager) {
        this.keyStatusManager.cacheDecryptedKey(cacheKey, apiKey);
      }

      this.logger.debug(`Successfully decrypted API key for keyId: ${encryptedData.keyId}`);
      return apiKey;
    } catch (error) {
      this.logger.error(`Failed to decrypt API key: ${error instanceof Error ? error.message : String(error)}`);
      throw new Error('Failed to decrypt API key');
    }
  }

  /**
   * Get encrypted API keys from etcd
   * Path: /api-keys/{provider}/{region}/*
   */
  async getEncryptedApiKeys(): Promise<EncryptedApiKeyData[]> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const prefix = `/api-keys/${this.provider}/${this.region}/`;
      const kvs = await etcdClient.getAll().prefix(prefix);
      const encryptedKeys: EncryptedApiKeyData[] = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const encryptedData = JSON.parse(jsonValue) as EncryptedApiKeyData;
          
          // Only include keys that are intended for this executor
          // Accept both 'active' and 'waiting-to-verify' status
          if (encryptedData.keyId === this.executorId &&
              (encryptedData.status === 'active' || encryptedData.status === 'waiting-to-verify')) {
            encryptedKeys.push(encryptedData);
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse encrypted key data for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
        }
      }

      this.logger.debug(`Found ${encryptedKeys.length} encrypted API keys for this executor`);
      return encryptedKeys;
    } catch (error) {
      this.logger.error(`Failed to get encrypted API keys: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }

  /**
   * Get and decrypt all available API keys
   * Now includes status filtering to skip failed keys
   */
  async getDecryptedApiKeys(): Promise<Array<{ uuid: string; apiKey: string; createdAt: string }>> {
    const encryptedKeys = await this.getEncryptedApiKeys();

    // Filter keys based on their status in etcd
    const availableKeys = await this.filterAvailableEncryptedKeys(encryptedKeys);

    const decryptedKeys: Array<{ uuid: string; apiKey: string; createdAt: string }> = [];

    for (const encryptedData of availableKeys) {
      try {
        const apiKey = await this.decryptApiKey(encryptedData);

        // Extract UUID from etcd path (last part of the path)
        const uuid = encryptedData.keyId || 'encrypted-key-' + Date.now();

        decryptedKeys.push({
          uuid,
          apiKey,
          createdAt: encryptedData.createdAt
        });

        // If this key was waiting-to-verify, mark it as active after successful decryption
        if (encryptedData.status === 'waiting-to-verify') {
          await this.markKeyAsActive(encryptedData);
        }
      } catch (error) {
        this.logger.warn(`Failed to decrypt key for keyId ${encryptedData.keyId}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    this.logger.log(`Successfully decrypted ${decryptedKeys.length} out of ${availableKeys.length} available keys (${encryptedKeys.length} total)`);
    return decryptedKeys;
  }

  /**
   * Mark a key as active after successful decryption verification
   */
  private async markKeyAsActive(encryptedData: EncryptedApiKeyData & { uuid?: string }): Promise<void> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        this.logger.warn('etcd client not available, cannot update key status');
        return;
      }

      // Find the key path in etcd
      const provider = this.configService.get<string>('EXECUTOR_MODEL_TYPE', 'openai');
      const region = this.configService.get<string>('EXECUTOR_REGION', 'asia');

      // We need to find the UUID from the etcd path
      const keyPrefix = `/api-keys/${provider}/${region}/`;
      const allKeys = await etcdClient.getAll().prefix(keyPrefix);

      for (const [keyPath, value] of Object.entries(allKeys)) {
        try {
          const keyData = JSON.parse(value) as EncryptedApiKeyData;

          // Match by keyId and other identifying data
          if (keyData.keyId === encryptedData.keyId &&
              keyData.encryptedKey === encryptedData.encryptedKey &&
              keyData.status === 'waiting-to-verify') {

            // Update status to active
            const updatedData = {
              ...keyData,
              status: 'active' as const,
            };

            await etcdClient.put(keyPath).value(JSON.stringify(updatedData));

            this.logger.log(`✅ Marked encrypted API key as active: ${keyPath}`);
            return;
          }
        } catch (parseError) {
          this.logger.debug(`Failed to parse key data at ${keyPath}: ${parseError}`);
        }
      }

      this.logger.warn(`Could not find encrypted key to mark as active for keyId: ${encryptedData.keyId}`);
    } catch (error) {
      this.logger.error(`Failed to mark key as active: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Filter encrypted keys based on their status in etcd
   * Skip keys that are rate_limited, exhausted, or revoked
   */
  private async filterAvailableEncryptedKeys(encryptedKeys: EncryptedApiKeyData[]): Promise<EncryptedApiKeyData[]> {
    const availableKeys: EncryptedApiKeyData[] = [];

    for (const encryptedKey of encryptedKeys) {
      try {
        // Check key status in etcd
        const keyStatus = await this.getKeyStatusFromEtcd(encryptedKey.keyId);

        if (!keyStatus) {
          // If no status found, assume key is available
          this.logger.debug(`No status found for key ${encryptedKey.keyId}, assuming available`);
          availableKeys.push(encryptedKey);
          continue;
        }

        // Skip keys with problematic status
        if (keyStatus.status === 'rate_limited' ||
            keyStatus.status === 'exhausted' ||
            keyStatus.status === 'revoked') {
          this.logger.debug(
            `Skipping key ${encryptedKey.keyId} due to status: ${keyStatus.status}` +
            (keyStatus.lastError ? ` (${keyStatus.lastError})` : '')
          );
          continue;
        }

        // Key is active, include it
        availableKeys.push(encryptedKey);
      } catch (error) {
        this.logger.warn(
          `Failed to check status for key ${encryptedKey.keyId}: ${error instanceof Error ? error.message : String(error)}`
        );
        // On error, include the key (fail-safe approach)
        availableKeys.push(encryptedKey);
      }
    }

    this.logger.log(
      `Filtered ${encryptedKeys.length} keys down to ${availableKeys.length} available keys for provider ${this.provider}`
    );

    return availableKeys;
  }

  /**
   * Get key status from etcd
   */
  private async getKeyStatusFromEtcd(keyId: string): Promise<any | null> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        return null;
      }

      const keyPath = `/keys/${this.provider}/${keyId}`;
      const statusJson = await etcdClient.get(keyPath);

      if (!statusJson) {
        return null;
      }

      return JSON.parse(statusJson);
    } catch (error) {
      this.logger.debug(`Failed to get key status for ${keyId}: ${error}`);
      return null;
    }
  }

  /**
   * Get public key for external use
   */
  getPublicKey(): string | null {
    if (!this.executorKeyPair) {
      return null;
    }
    return this.uint8ArrayToBase64(this.executorKeyPair.publicKey);
  }

  /**
   * Get executor information
   */
  getExecutorInfo(): { id: string; provider: string; region: string; publicKey: string | null } {
    return {
      id: this.executorId,
      provider: this.provider,
      region: this.region,
      publicKey: this.getPublicKey()
    };
  }

  /**
   * Utility: Convert Uint8Array to base64
   */
  private uint8ArrayToBase64(array: Uint8Array): string {
    return Buffer.from(array).toString('base64');
  }

  /**
   * Utility: Convert base64 to Uint8Array
   */
  private base64ToUint8Array(base64: string): Uint8Array {
    return new Uint8Array(Buffer.from(base64, 'base64'));
  }

  /**
   * Test encryption/decryption functionality
   */
  async testEncryptionFlow(testApiKey: string = 'sk-test1234567890'): Promise<boolean> {
    if (!this.executorKeyPair) {
      this.logger.error('Cannot test encryption: key pair not initialized');
      return false;
    }

    try {
      // Simulate the encryption process (normally done by frontend)
      const ephemeralKeyPair = nacl.box.keyPair();
      const sharedSecret = nacl.scalarMult(ephemeralKeyPair.secretKey, this.executorKeyPair.publicKey);
      
      const encoder = new TextEncoder();
      const nonce = nacl.randomBytes(12);
      const aead = chacha20poly1305(sharedSecret, nonce);
      const encrypted = aead.encrypt(encoder.encode(testApiKey));
      
      const tagLength = 16;
      const ciphertext = encrypted.slice(0, encrypted.length - tagLength);
      const tag = encrypted.slice(encrypted.length - tagLength);

      const encryptedData: EncryptedApiKeyData = {
        keyId: this.executorId,
        encryptedKey: this.uint8ArrayToBase64(ciphertext),
        nonce: this.uint8ArrayToBase64(nonce),
        tag: this.uint8ArrayToBase64(tag),
        ephemeralPubKey: this.uint8ArrayToBase64(ephemeralKeyPair.publicKey),
        status: 'active',
        createdAt: new Date().toISOString()
      };

      // Test decryption
      const decryptedKey = await this.decryptApiKey(encryptedData);
      
      const success = decryptedKey === testApiKey;
      this.logger.log(`Encryption test ${success ? 'PASSED' : 'FAILED'}`);
      
      return success;
    } catch (error) {
      this.logger.error(`Encryption test failed: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }
}
