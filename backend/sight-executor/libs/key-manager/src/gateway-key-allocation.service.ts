import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import * as crypto from 'crypto';
import { EncryptionService } from './encryption.service';
import { ManagedApiKey } from '@app/types';

export interface AllocatedApiKey {
  uuid: string;
  encryptedKey: string;
  nonce: string;
  tag: string;
  ephemeralPubKey: string;
  status: 'active' | 'revoked' | 'waiting-to-verify';
  createdAt: string;
  scope: string;
}

export interface KeyAllocationResponse {
  success: boolean;
  data: {
    keys: AllocatedApiKey[];
    totalCount: number;
    scope: string;
    allocationId: string; // Unique allocation ID for tracking
  };
}

@Injectable()
export class GatewayKeyAllocationService implements OnModuleInit {
  private readonly logger = new Logger(GatewayKeyAllocationService.name);
  private httpClient: AxiosInstance;
  private allocatedKeys: Map<string, ManagedApiKey> = new Map();
  private allocationId: string | null = null;
  
  private readonly gatewayUrl: string;
  private readonly executorId: string;
  private readonly executorRegion: string;
  private readonly executorModelType: string;
  private readonly executorSecretKey: string;

  constructor(
    private configService: ConfigService,
    private encryptionService: EncryptionService,
  ) {
    this.gatewayUrl = this.configService.get<string>('gateway.url', 'http://localhost:3000');
    this.executorId = this.configService.get<string>('executor.id', 'executor-001');
    this.executorRegion = this.configService.get<string>('executor.region', 'default');
    this.executorModelType = this.configService.get<string>('executor.modelType', 'openai');
    this.executorSecretKey = this.configService.get<string>('EXECUTOR_SECRET_KEY', 'default-secret-key');

    this.initializeHttpClient();
  }

  async onModuleInit() {
    // Wait for executor registration to complete before requesting keys
    this.logger.log('Waiting for executor registration to complete...');

    // Don't block application startup if key allocation fails
    setTimeout(async () => {
      try {
        await this.requestKeyAllocation();
      } catch (error) {
        this.logger.error(`Failed to request initial key allocation: ${error.message}`);
        this.logger.log('Will retry key allocation in 30 seconds...');
      }
    }, 5000); // Wait 5 seconds

    // Set up periodic key refresh (every 30 minutes)
    setInterval(() => {
      this.refreshKeyAllocation().catch(error => {
        this.logger.error(`Failed to refresh key allocation: ${error.message}`);
      });
    }, 30 * 60 * 1000); // 30 minutes
  }

  private initializeHttpClient(): void {
    this.httpClient = axios.create({
      baseURL: this.gatewayUrl,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': `sight-executor/${this.executorId}`,
      },
    });

    // Add request interceptor to include executor authentication
    this.httpClient.interceptors.request.use((config) => {
      const executorSecret = this.generateExecutorSecret();
      config.headers['x-executor-id'] = this.executorId;
      config.headers['x-executor-secret'] = executorSecret;
      config.headers['x-executor-region'] = this.executorRegion;
      return config;
    });

    // Add response interceptor for error handling
    this.httpClient.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error(`Gateway request failed: ${error.response?.status} ${error.response?.statusText}`);
        if (error.response?.data) {
          this.logger.error(`Error details:`, error.response.data);
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Generate executor secret for authentication
   */
  private generateExecutorSecret(): string {
    const payload = `${this.executorId}:${this.executorRegion}:${this.executorModelType}:${this.executorSecretKey}`;
    return crypto.createHash('sha256').update(payload).digest('hex').substring(0, 32);
  }

  /**
   * Request initial API key allocation from Gateway (fallback to direct etcd access)
   */
  async requestKeyAllocation(): Promise<void> {
    try {
      const scope = `${this.executorModelType}:${this.executorRegion}`;
      this.logger.log(`Requesting API key allocation for scope: ${scope}`);

      try {
        // Try Gateway first
        const response = await this.httpClient.get<KeyAllocationResponse>('/executor/key', {
          params: { scope }
        });

        if (response.data.success && response.data.data.keys.length > 0) {
          await this.processAllocatedKeys(response.data.data);
          this.allocationId = response.data.data.allocationId;
          this.logger.log(`Successfully allocated ${response.data.data.keys.length} API keys from Gateway`);
          return;
        }
      } catch (gatewayError) {
        this.logger.warn(`Gateway allocation failed, falling back to direct etcd access: ${gatewayError.message}`);
      }

      // Fallback: Get keys directly from etcd
      await this.requestKeysFromEtcd();
    } catch (error) {
      this.logger.error(`Failed to request key allocation: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Fallback method to get keys directly from etcd
   */
  private async requestKeysFromEtcd(): Promise<void> {
    try {
      this.logger.log('Getting API keys directly from etcd...');

      // Get all available encrypted keys for this executor
      const encryptedKeys = await this.encryptionService.getDecryptedApiKeys();

      if (encryptedKeys.length === 0) {
        this.logger.warn('No encrypted API keys available for allocation');
        return;
      }

      this.logger.log(`Found ${encryptedKeys.length} encrypted keys to allocate`);

      // Process each encrypted key
      for (const keyData of encryptedKeys) {
        // Skip if already allocated
        if (this.allocatedKeys.has(keyData.uuid)) {
          this.logger.debug(`Key ${keyData.uuid} already allocated, skipping`);
          continue;
        }

        const managedKey: ManagedApiKey = {
          id: keyData.uuid,
          key: keyData.apiKey,
          status: 'active',
          lastUsedAt: 0,
          lastError: undefined,
          note: 'Direct etcd allocation',
          usageCount: 0,
          errorCount: 0,
          keyId: keyData.uuid,
          lockedAt: 0,
          lockTimeout: 0,
        };

        this.allocatedKeys.set(keyData.uuid, managedKey);
        this.logger.debug(`Processed allocated key: ${keyData.uuid}`);
      }

      this.logger.log(`Successfully allocated ${this.allocatedKeys.size} API keys from etcd (${encryptedKeys.length} total available)`);
    } catch (error) {
      this.logger.error(`Failed to get keys from etcd: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Refresh key allocation (get new keys, release expired ones)
   */
  async refreshKeyAllocation(): Promise<void> {
    this.logger.debug('Refreshing API key allocation...');
    
    try {
      // Clean up expired keys first
      this.cleanupExpiredKeys();
      
      // Request new allocation if we have few keys left
      if (this.allocatedKeys.size < 2) {
        await this.requestKeyAllocation();
      }
    } catch (error) {
      this.logger.error(`Failed to refresh key allocation: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Process allocated keys from Gateway response
   */
  private async processAllocatedKeys(allocationData: KeyAllocationResponse['data']): Promise<void> {
    for (const allocatedKey of allocationData.keys) {
      try {
        // Decrypt the API key
        const decryptedApiKey = await this.encryptionService.decryptApiKey({
          keyId: this.executorId,
          encryptedKey: allocatedKey.encryptedKey,
          nonce: allocatedKey.nonce,
          tag: allocatedKey.tag,
          ephemeralPubKey: allocatedKey.ephemeralPubKey,
          status: allocatedKey.status,
          createdAt: allocatedKey.createdAt,
        });

        // Create managed key object
        const managedKey: ManagedApiKey = {
          id: allocatedKey.uuid,
          key: decryptedApiKey,
          status: 'active',
          lastUsedAt: 0,
          lastError: undefined,
          note: `Allocated from Gateway - ${allocatedKey.scope}`,
          usageCount: 0,
          errorCount: 0,
          keyId: allocatedKey.uuid,
          lockedAt: undefined,
          lockTimeout: undefined,
        };

        this.allocatedKeys.set(allocatedKey.uuid, managedKey);
        this.logger.debug(`Processed allocated key: ${allocatedKey.uuid}`);
      } catch (error) {
        this.logger.error(`Failed to process allocated key ${allocatedKey.uuid}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  /**
   * Get an available allocated API key with round-robin selection
   */
  async getAvailableKey(): Promise<ManagedApiKey | null> {
    // Get all available keys (not in use)
    const availableKeys = Array.from(this.allocatedKeys.entries())
      .filter(([keyId, managedKey]) => managedKey.status === 'active')
      .map(([keyId, managedKey]) => ({ keyId, managedKey }));

    if (availableKeys.length === 0) {
      this.logger.warn('No available allocated keys found');

      // Try to request more keys if none available
      if (this.allocatedKeys.size === 0) {
        await this.requestKeyAllocation();
        // Retry once after allocation
        return this.getAvailableKey();
      }
      return null;
    }

    // Use round-robin selection to distribute load across keys
    // Sort by last used time (least recently used first)
    availableKeys.sort((a, b) => (a.managedKey.lastUsedAt || 0) - (b.managedKey.lastUsedAt || 0));

    const selected = availableKeys[0];
    const { keyId, managedKey } = selected;

    // Mark as in use
    managedKey.status = 'in_use';
    managedKey.lockedAt = Date.now();
    managedKey.lockTimeout = Date.now() + 5 * 60 * 1000; // 5 minutes timeout
    managedKey.lastUsedAt = Date.now();

    this.logger.debug(`Allocated key ${keyId} for use (LRU selection)`);
    return managedKey;
  }

  /**
   * Release a key back to available pool
   */
  async releaseKey(keyId: string): Promise<boolean> {
    const managedKey = this.allocatedKeys.get(keyId);
    if (!managedKey) {
      this.logger.warn(`Key ${keyId} not found in allocated keys`);
      return false;
    }

    managedKey.status = 'active';
    managedKey.lockedAt = undefined;
    managedKey.lockTimeout = undefined;

    this.logger.debug(`Released key ${keyId} back to active pool`);
    return true;
  }

  /**
   * Handle API error and update key status with enhanced OpenAI error classification
   */
  async handleApiError(keyId: string, error: any): Promise<void> {
    const managedKey = this.allocatedKeys.get(keyId);
    if (!managedKey) {
      return;
    }

    const errorMessage = error?.message || String(error);
    const statusCode = error?.status || error?.response?.status || 0;
    const errorCode = error?.code || error?.error?.code || '';

    managedKey.errorCount++;
    managedKey.lastError = errorMessage;

    this.logger.debug(`Handling API error for key ${keyId}: ${statusCode} - ${errorCode} - ${errorMessage}`);

    // Enhanced error handling based on OpenAI official error codes
    if (statusCode === 401) {
      // Authentication errors - remove key permanently
      this.logger.error(`Key ${keyId} authentication failed, removing from pool: ${errorMessage}`);
      this.allocatedKeys.delete(keyId);
      await this.requestKeyAllocation(); // Request replacement
      return;
    } else if (statusCode === 429) {
      // Rate limit and quota errors
      if (errorMessage.toLowerCase().includes('quota') ||
          errorMessage.toLowerCase().includes('billing') ||
          errorMessage.toLowerCase().includes('credits')) {
        // Quota exhausted - remove permanently
        this.logger.error(`Key ${keyId} quota exhausted, removing from pool: ${errorMessage}`);
        this.allocatedKeys.delete(keyId);
        await this.requestKeyAllocation(); // Request replacement
        return;
      } else {
        // Rate limited - temporarily mark as unavailable
        managedKey.status = 'rate_limited';
        managedKey.lockTimeout = Date.now() + 5 * 60 * 1000; // 5 minutes
        this.logger.warn(`Key ${keyId} rate limited for 5 minutes: ${errorMessage}`);
        return;
      }
    } else if (statusCode === 403) {
      // Forbidden - could be region restriction
      if (errorMessage.toLowerCase().includes('country') ||
          errorMessage.toLowerCase().includes('region') ||
          errorMessage.toLowerCase().includes('territory')) {
        this.logger.warn(`Key ${keyId} region restricted: ${errorMessage}`);
        // Don't remove, might work for other requests
        await this.releaseKey(keyId);
        return;
      } else {
        this.logger.error(`Key ${keyId} access forbidden, removing from pool: ${errorMessage}`);
        this.allocatedKeys.delete(keyId);
        await this.requestKeyAllocation(); // Request replacement
        return;
      }
    } else if (statusCode >= 500) {
      // Server errors - don't mark key as bad, just release
      this.logger.warn(`Server error for key ${keyId}, releasing: ${statusCode} - ${errorMessage}`);
      await this.releaseKey(keyId);
      return;
    } else if (errorCode === 'model_not_found' ||
               (errorMessage.toLowerCase().includes('model') &&
                errorMessage.toLowerCase().includes('does not exist'))) {
      // Model access error - don't mark key as bad, just release
      this.logger.debug(`Key ${keyId} doesn't support requested model, releasing: ${errorMessage}`);
      await this.releaseKey(keyId);
      return;
    }

    // For other errors, check if it's a known invalid key error
    if (this.isKeyInvalidError(error)) {
      this.logger.warn(`Removing invalid key ${keyId} due to error: ${managedKey.lastError}`);
      this.allocatedKeys.delete(keyId);
      await this.requestKeyAllocation();
    } else {
      // Release the key for retry
      await this.releaseKey(keyId);
    }
  }

  /**
   * Check if error indicates the key is invalid and should be removed
   */
  private isKeyInvalidError(error: any): boolean {
    const errorMessage = (error?.message || error?.toString() || '').toLowerCase();
    
    // Check for quota exhaustion, invalid key, etc.
    return errorMessage.includes('insufficient_quota') ||
           errorMessage.includes('invalid_api_key') ||
           errorMessage.includes('account_deactivated') ||
           errorMessage.includes('billing_not_active');
  }

  /**
   * Clean up expired keys
   */
  private cleanupExpiredKeys(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [keyId, managedKey] of this.allocatedKeys.entries()) {
      if (managedKey.lockTimeout && now > managedKey.lockTimeout) {
        expiredKeys.push(keyId);
      }
    }

    for (const keyId of expiredKeys) {
      this.releaseKey(keyId);
      this.logger.debug(`Released expired key: ${keyId}`);
    }
  }

  /**
   * Get allocation statistics
   */
  getAllocationStats(): {
    totalKeys: number;
    availableKeys: number;
    inUseKeys: number;
    allocationId: string | null;
  } {
    let availableKeys = 0;
    let inUseKeys = 0;

    for (const managedKey of this.allocatedKeys.values()) {
      if (managedKey.status === 'active') {
        availableKeys++;
      } else if (managedKey.status === 'in_use') {
        inUseKeys++;
      }
    }

    return {
      totalKeys: this.allocatedKeys.size,
      availableKeys,
      inUseKeys,
      allocationId: this.allocationId,
    };
  }

  /**
   * Check if service has available keys
   */
  hasAvailableKeys(): boolean {
    for (const managedKey of this.allocatedKeys.values()) {
      if (managedKey.status === 'active') {
        return true;
      }
    }
    return false;
  }
}
