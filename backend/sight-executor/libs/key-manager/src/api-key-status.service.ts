import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { EtcdService } from '@app/etcd';
import { ConfigService } from '@nestjs/config';
import { ApiKeyStatus } from '@app/types';

/**
 * API Key Status Service
 * Manages API key status in etcd according to design document
 * Path: /keys/{provider}/{key_id}
 */
@Injectable()
export class ApiKeyStatusService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ApiKeyStatusService.name);
  private readonly provider: string;
  private statusWatcher: any = null;

  constructor(
    private etcdService: EtcdService,
    private configService: ConfigService,
  ) {
    this.provider = this.configService.get<string>('executor.modelType', 'openai');
  }

  async onModuleInit() {
    this.logger.log(`API Key Status Service initialized for provider: ${this.provider}`);
    await this.startStatusWatcher();
  }

  async onModuleDestroy() {
    if (this.statusWatcher) {
      await this.statusWatcher.cancel();
      this.statusWatcher = null;
    }
    this.logger.log('API Key Status Service destroyed');
  }

  /**
   * Get API key status from etcd
   * Path: /keys/{provider}/{key_id}
   */
  async getKeyStatus(keyId: string): Promise<ApiKeyStatus | null> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const keyPath = `/keys/${this.provider}/${keyId}`;
      const value = await etcdClient.get(keyPath);

      if (!value) {
        return null;
      }

      const status = JSON.parse(value) as ApiKeyStatus;
      this.logger.debug(`Retrieved status for key ${keyId}: ${status.status}`);
      return status;
    } catch (error) {
      this.logger.error(`Failed to get key status for ${keyId}: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * Update API key status in etcd
   * Path: /keys/{provider}/{key_id}
   */
  async updateKeyStatus(
    keyId: string, 
    status: 'active' | 'rate_limited' | 'exhausted' | 'revoked',
    lastError?: string,
    note?: string
  ): Promise<boolean> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const keyPath = `/keys/${this.provider}/${keyId}`;
      
      // Get existing status or create new one
      let existingStatus: ApiKeyStatus | null = null;
      try {
        const existingValue = await etcdClient.get(keyPath);
        if (existingValue) {
          existingStatus = JSON.parse(existingValue) as ApiKeyStatus;
        }
      } catch (error) {
        this.logger.debug(`No existing status found for key ${keyId}, creating new one`);
      }

      const updatedStatus: ApiKeyStatus = {
        key: existingStatus?.key || `sk-${keyId}`, // Placeholder key format
        status,
        lastUsedAt: Date.now(),
        lastError,
        note
      };

      await etcdClient.put(keyPath).value(JSON.stringify(updatedStatus));
      
      this.logger.log(`Updated key ${keyId} status to ${status}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update key status for ${keyId}: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Get all API keys with their status for the current provider
   * Path: /keys/{provider}/*
   */
  async getAllKeyStatuses(): Promise<Map<string, ApiKeyStatus>> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        throw new Error('etcd client is not initialized');
      }

      const prefix = `/keys/${this.provider}/`;
      const kvs = await etcdClient.getAll().prefix(prefix);
      const statusMap = new Map<string, ApiKeyStatus>();

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyId = fullPath.replace(prefix, '');
          const status = JSON.parse(jsonValue) as ApiKeyStatus;
          statusMap.set(keyId, status);
        } catch (parseError) {
          this.logger.warn(`Failed to parse key status for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
        }
      }

      this.logger.debug(`Retrieved ${statusMap.size} key statuses for provider ${this.provider}`);
      return statusMap;
    } catch (error) {
      this.logger.error(`Failed to get all key statuses: ${error instanceof Error ? error.message : String(error)}`);
      return new Map();
    }
  }

  /**
   * Get active API keys for the current provider
   */
  async getActiveKeys(): Promise<string[]> {
    const allStatuses = await this.getAllKeyStatuses();
    const activeKeys: string[] = [];

    for (const [keyId, status] of allStatuses.entries()) {
      if (status.status === 'active') {
        activeKeys.push(keyId);
      }
    }

    this.logger.debug(`Found ${activeKeys.length} active keys for provider ${this.provider}`);
    return activeKeys;
  }

  /**
   * Mark key as rate limited
   */
  async markKeyRateLimited(keyId: string, reason?: string): Promise<boolean> {
    return this.updateKeyStatus(keyId, 'rate_limited', reason);
  }

  /**
   * Mark key as exhausted
   */
  async markKeyExhausted(keyId: string, reason?: string): Promise<boolean> {
    return this.updateKeyStatus(keyId, 'exhausted', reason);
  }

  /**
   * Mark key as revoked
   */
  async markKeyRevoked(keyId: string, reason?: string): Promise<boolean> {
    return this.updateKeyStatus(keyId, 'revoked', reason);
  }

  /**
   * Mark key as active
   */
  async markKeyActive(keyId: string, note?: string): Promise<boolean> {
    return this.updateKeyStatus(keyId, 'active', undefined, note);
  }

  /**
   * Check if a key is available (active status)
   */
  async isKeyAvailable(keyId: string): Promise<boolean> {
    const status = await this.getKeyStatus(keyId);
    return status?.status === 'active';
  }

  /**
   * Start watching for key status changes
   */
  private async startStatusWatcher(): Promise<void> {
    try {
      const etcdClient = this.etcdService.getEtcdClient();
      if (!etcdClient) {
        this.logger.warn('etcd client not available, status watcher not started');
        return;
      }

      const watchPrefix = `/keys/${this.provider}/`;
      
      // Watch for changes in key statuses
      this.statusWatcher = etcdClient.watch().prefix(watchPrefix).create();
      
      this.statusWatcher.on('put', (event: any) => {
        try {
          const keyPath = event.key.toString();
          const keyId = keyPath.replace(watchPrefix, '');
          const status = JSON.parse(event.value.toString()) as ApiKeyStatus;
          
          this.logger.debug(`Key status changed: ${keyId} -> ${status.status}`);
          
          // Emit event or handle status change as needed
          this.handleKeyStatusChange(keyId, status);
        } catch (error) {
          this.logger.error(`Failed to process key status change: ${error instanceof Error ? error.message : String(error)}`);
        }
      });

      this.statusWatcher.on('delete', (event: any) => {
        try {
          const keyPath = event.key.toString();
          const keyId = keyPath.replace(watchPrefix, '');
          
          this.logger.debug(`Key status deleted: ${keyId}`);
          this.handleKeyStatusDelete(keyId);
        } catch (error) {
          this.logger.error(`Failed to process key status deletion: ${error instanceof Error ? error.message : String(error)}`);
        }
      });

      this.logger.log(`Started watching key status changes with prefix: ${watchPrefix}`);
    } catch (error) {
      this.logger.error(`Failed to start status watcher: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Handle key status change events
   */
  private handleKeyStatusChange(keyId: string, status: ApiKeyStatus): void {
    // Log the change
    this.logger.log(`Key ${keyId} status changed to ${status.status}`);
    
    // Additional handling can be added here
    // For example, notifying other services, updating local caches, etc.
  }

  /**
   * Handle key status deletion events
   */
  private handleKeyStatusDelete(keyId: string): void {
    this.logger.log(`Key ${keyId} status was deleted`);
    
    // Additional handling can be added here
  }

  /**
   * Get key status statistics
   */
  async getStatusStatistics(): Promise<{
    total: number;
    active: number;
    rateLimited: number;
    exhausted: number;
    revoked: number;
  }> {
    const allStatuses = await this.getAllKeyStatuses();
    
    const stats = {
      total: allStatuses.size,
      active: 0,
      rateLimited: 0,
      exhausted: 0,
      revoked: 0
    };

    for (const status of allStatuses.values()) {
      switch (status.status) {
        case 'active':
          stats.active++;
          break;
        case 'rate_limited':
          stats.rateLimited++;
          break;
        case 'exhausted':
          stats.exhausted++;
          break;
        case 'revoked':
          stats.revoked++;
          break;
      }
    }

    return stats;
  }
}
