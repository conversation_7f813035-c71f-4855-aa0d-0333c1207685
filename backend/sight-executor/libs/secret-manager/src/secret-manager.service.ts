import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

export interface ExecutorKeyPair {
  publicKey: string;  // base64 encoded X25519 public key
  privateKey: string; // base64 encoded X25519 private key
  keyId: string;      // unique identifier for this key pair
  createdAt: string;  // ISO8601 timestamp
}

@Injectable()
export class SecretManagerService {
  private readonly logger = new Logger(SecretManagerService.name);
  private secretClient: SecretManagerServiceClient;
  private readonly projectId: string;
  private readonly executorId: string;

  constructor(private configService: ConfigService) {
    this.projectId = this.configService.get<string>('GCP_KMS_PROJECT_ID') || '';
    this.executorId = this.configService.get<string>('executor.id', 'executor-' + Date.now());

    // 只在生产环境或明确配置了 GCP 项目时才要求 GCP 配置
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    const hasLocalPrivateKey = this.configService.get<string>('EXECUTOR_PRIVATE_KEY');

    if (!this.projectId && nodeEnv === 'production') {
      throw new Error('GCP project ID is required in production. Please set GCP_KMS_PROJECT_ID');
    }

    // 只有在配置了项目 ID 时才初始化 Secret Client
    if (this.projectId) {
      this.initializeSecretClient();
    } else {
      this.logger.warn('GCP_KMS_PROJECT_ID not configured. Secret Manager will be disabled.');
      if (!hasLocalPrivateKey) {
        this.logger.warn('No EXECUTOR_PRIVATE_KEY found. Key pair will be generated automatically.');
      }
    }
  }

  private initializeSecretClient(): void {
    try {
      this.secretClient = new SecretManagerServiceClient();
      this.logger.log(`GCP Secret Manager client initialized for project: ${this.projectId}`);
    } catch (error) {
      this.logger.error(`Failed to initialize GCP Secret Manager client: ${error}`);
      throw error;
    }
  }

  /**
   * Get executor private key from Secret Manager
   * This is called during Executor startup
   */
  async getExecutorPrivateKey(): Promise<string> {
    try {
      // Try multiple secret naming patterns
      const secretIds = [
        this.configService.get<string>('EXECUTOR_SECRET_ID', `executor-keypair-${this.executorId}`),
        `executor-keypair-${this.executorId}`,
        'executor-keypair-001', // Default fallback
      ];

      for (const secretId of secretIds) {
        try {
          this.logger.debug(`Trying secret ID: ${secretId}`);
          const keyPair = await this.getExecutorKeyPair(secretId);

          this.logger.log(`✅ Successfully retrieved private key from secret: ${secretId}`);
          return keyPair.privateKey;
        } catch (error) {
          this.logger.debug(`Secret ${secretId} not found or inaccessible: ${error instanceof Error ? error.message : String(error)}`);
          continue;
        }
      }

      throw new Error(`No accessible executor key pair found in any of the tried secret IDs: ${secretIds.join(', ')}`);
    } catch (error) {
      this.logger.error(`Failed to get executor private key: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get complete executor key pair from Secret Manager
   */
  async getExecutorKeyPair(secretId?: string): Promise<ExecutorKeyPair> {
    try {
      const actualSecretId = secretId || `executor-keypair-${this.executorId}`;
      const payload = await this.accessSecret(actualSecretId);
      
      const keyPair = JSON.parse(payload) as ExecutorKeyPair;
      
      // Validate the key pair structure
      if (!keyPair.publicKey || !keyPair.privateKey || !keyPair.keyId) {
        throw new Error('Invalid key pair structure in secret');
      }

      this.logger.debug(`Retrieved executor key pair: ${keyPair.keyId}`);
      return keyPair;
    } catch (error) {
      this.logger.error(`Failed to get executor key pair: ${error}`);
      throw error;
    }
  }

  /**
   * Access the latest version of a secret
   */
  private async accessSecret(secretId: string, version: string = 'latest'): Promise<string> {
    try {
      const name = `projects/${this.projectId}/secrets/${secretId}/versions/${version}`;
      
      const [accessResponse] = await this.secretClient.accessSecretVersion({
        name: name,
      });

      if (!accessResponse.payload?.data) {
        throw new Error('Secret payload is empty');
      }

      const payload = accessResponse.payload.data.toString('utf8');
      this.logger.debug(`Successfully accessed secret: ${secretId}`);
      
      return payload;
    } catch (error) {
      this.logger.error(`Failed to access secret ${secretId}: ${error}`);
      throw error;
    }
  }

  /**
   * Test Secret Manager connectivity
   */
  async testConnection(): Promise<boolean> {
    // Return false if Secret Manager is not configured
    if (!this.projectId || !this.secretClient) {
      this.logger.debug('Secret Manager not configured, skipping connection test');
      return false;
    }

    try {
      this.logger.log('Testing Secret Manager connection...');

      // Test by listing secrets (this requires minimal permissions)
      const parent = `projects/${this.projectId}`;
      await this.secretClient.listSecrets({
        parent: parent,
        pageSize: 1,
      });

      this.logger.log('Secret Manager connection test successful');
      return true;
    } catch (error) {
      this.logger.error(`Secret Manager connection test failed: ${error}`);
      return false;
    }
  }

  /**
   * Check if executor key pair exists in Secret Manager
   */
  async hasExecutorKeyPair(): Promise<boolean> {
    try {
      const secretId = `executor-keypair-${this.executorId}`;
      await this.accessSecret(secretId);
      return true;
    } catch (error) {
      this.logger.debug(`Executor key pair not found: ${error}`);
      return false;
    }
  }

  /**
   * Get executor ID
   */
  getExecutorId(): string {
    return this.executorId;
  }

  /**
   * Get project ID
   */
  getProjectId(): string {
    return this.projectId;
  }

  /**
   * Fallback: Load private key from environment variable
   * Used when Secret Manager is not available or in development
   */
  async getPrivateKeyFromEnv(): Promise<string> {
    try {
      const privateKeyBase64 = this.configService.get<string>('EXECUTOR_PRIVATE_KEY');
      
      if (!privateKeyBase64) {
        throw new Error('EXECUTOR_PRIVATE_KEY environment variable is not set');
      }

      // Validate base64 format
      try {
        const privateKeyBuffer = Buffer.from(privateKeyBase64, 'base64');
        if (privateKeyBuffer.length !== 32) {
          throw new Error('Private key must be 32 bytes (X25519)');
        }
      } catch (error) {
        throw new Error(`Invalid private key format: ${error}`);
      }

      this.logger.log('Using private key from environment variable (fallback mode)');
      return privateKeyBase64;
    } catch (error) {
      this.logger.error(`Failed to get private key from environment: ${error}`);
      throw error;
    }
  }

  /**
   * Load private key with fallback strategy
   * 1. Try Secret Manager first (if configured)
   * 2. Fall back to environment variable
   * 3. Return null if neither available (let caller handle)
   */
  async loadPrivateKey(): Promise<string | null> {
    try {
      // Only try Secret Manager if GCP is configured
      if (this.projectId && this.secretClient) {
        if (await this.testConnection()) {
          try {
            return await this.getExecutorPrivateKey();
          } catch (secretError) {
            this.logger.warn(`Failed to load from Secret Manager, trying fallback: ${secretError}`);
          }
        }
      } else {
        this.logger.debug('GCP Secret Manager not configured, skipping...');
      }

      // Fallback to environment variable
      try {
        return await this.getPrivateKeyFromEnv();
      } catch (envError) {
        this.logger.debug(`No private key in environment variable: ${envError}`);
      }

      // Return null if no key found (let caller decide what to do)
      this.logger.debug('No private key found in Secret Manager or environment variables');
      return null;
    } catch (error) {
      this.logger.error(`Failed to load private key: ${error}`);
      return null;
    }
  }

  /**
   * Get secret information for debugging
   */
  async getSecretInfo(secretId?: string): Promise<{
    secretId: string;
    projectId: string;
    executorId: string;
    exists: boolean;
  }> {
    const actualSecretId = secretId || `executor-keypair-${this.executorId}`;
    const exists = await this.hasExecutorKeyPair();

    return {
      secretId: actualSecretId,
      projectId: this.projectId,
      executorId: this.executorId,
      exists,
    };
  }
}
