import { Module } from '@nestjs/common';
import { OpenAIProvider } from './openai.provider';
import { ProviderFactoryService } from './provider-factory.service';
import { OpenAIErrorClassifierService } from './openai-error-classifier.service';
import { KeyManagerModule } from '@app/key-manager';

@Module({
  imports: [KeyManagerModule],
  providers: [OpenAIProvider, ProviderFactoryService, OpenAIErrorClassifierService],
  exports: [ProviderFactoryService],
})
export class ProvidersModule {}
