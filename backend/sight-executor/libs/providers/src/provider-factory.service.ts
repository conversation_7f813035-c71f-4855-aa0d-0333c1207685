import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  BaseModelProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ProviderError,
} from './base-provider.interface';
import { OpenAIProvider } from './openai.provider';
import { KeyManagerService } from '@app/key-manager';
import { ManagedApiKey } from '@app/types';
import { OpenAIErrorClassifierService, OpenAIErrorType } from './openai-error-classifier.service';

@Injectable()
export class ProviderFactoryService {
  private readonly logger = new Logger(ProviderFactoryService.name);
  private readonly providers: Map<string, BaseModelProvider> = new Map();
  private readonly modelType: string;

  constructor(
    private configService: ConfigService,
    private keyManagerService: KeyManagerService,
    private openaiProvider: OpenAIProvider,
    private errorClassifier: OpenAIErrorClassifierService,
  ) {
    this.modelType = this.configService.get<string>(
      'executor.modelType',
      'openai',
    );
    this.initializeProviders();
  }

  private initializeProviders(): void {
    this.providers.set('openai', this.openaiProvider);

    this.logger.log(
      `Initialized providers: ${Array.from(this.providers.keys()).join(', ')}`,
    );
    this.logger.log(`Current model type: ${this.modelType}`);
  }

  async executeRequest(
    request: ChatCompletionRequest,
  ): Promise<ChatCompletionResponse | AsyncIterable<string>> {
    this.logger.debug(`Starting request execution for model: ${request.model}`);

    const provider = this.providers.get(this.modelType);
    if (!provider) {
      this.logger.error(`Provider not found for model type: ${this.modelType}`);
      throw new Error(`Provider not found for model type: ${this.modelType}`);
    }
    this.logger.debug(`Provider found: ${provider.providerName}`);

    // Validate model if provider supports it
    if (provider.validateModel && !provider.validateModel(request.model)) {
      this.logger.error(
        `Model ${request.model} is not supported by provider ${this.modelType}`,
      );
      throw new Error(
        `Model ${request.model} is not supported by provider ${this.modelType}`,
      );
    }

    let lastError: ProviderError | null = null;
    let currentApiKey: ManagedApiKey | null = null;
    const maxKeyAttempts = this.configService.get<number>('executor.maxKeyAttempts', 5);
    const usedKeyIds = new Set<string>(); // 记录已经尝试过的key

    try {
      // 尝试多个不同的key
      for (let keyAttempt = 1; keyAttempt <= maxKeyAttempts; keyAttempt++) {
        // 获取一个新的可用key
        this.logger.debug(`Getting available API key (attempt ${keyAttempt}/${maxKeyAttempts})...`);
        currentApiKey = await this.keyManagerService.getAndLockKey();

        if (!currentApiKey) {
          this.logger.warn(`No available API keys found on attempt ${keyAttempt}`);
          if (keyAttempt === maxKeyAttempts) {
            throw new Error('No available API keys after trying all attempts');
          }
          continue;
        }

        // 检查是否已经尝试过这个key
        if (usedKeyIds.has(currentApiKey.id)) {
          this.logger.debug(`Key ${currentApiKey.id} already tried, releasing and getting another`);
          await this.keyManagerService.releaseKey(currentApiKey.id);
          continue;
        }

        usedKeyIds.add(currentApiKey.id);

        // 安全检查 key 字段
        if (!currentApiKey.key) {
          this.logger.error(`API key ${currentApiKey.id} has no key value, skipping`);
          await this.keyManagerService.releaseKey(currentApiKey.id);
          continue;
        }

        this.logger.debug(
          `Using API key: ${currentApiKey.id}, key starts with: ${currentApiKey.key.substring(0, 10)}... (attempt ${keyAttempt}/${maxKeyAttempts})`,
        );

        try {
          // 尝试使用当前key执行请求
          const response = await provider.chatCompletion(
            request,
            currentApiKey.key,
          );

          this.logger.debug(
            `Request completed successfully with key ${currentApiKey.id}`,
          );
          return response;
        } catch (error) {
          lastError = error as ProviderError;
          this.logger.warn(
            `Key ${currentApiKey.id} failed: ${lastError.code} - ${lastError.message}`,
          );

          // 使用新的错误分类器分析错误
          const errorClassification = this.errorClassifier.classifyError(lastError);

          this.logger.debug(
            `Error classification for key ${currentApiKey.id}: ${errorClassification.type} - ${errorClassification.description}`,
          );

          // 根据分类结果决定是否标记 Key 状态
          if (errorClassification.shouldMarkKeyBad) {
            await this.updateKeyStatusOnError(currentApiKey.id, lastError, errorClassification);
          }

          // 如果需要通知其他 Executor
          if (errorClassification.shouldNotifyOtherExecutors) {
            this.logger.warn(`Key ${currentApiKey.id} requires notification to other executors: ${errorClassification.description}`);
          }

          // Release the current key since it failed
          await this.keyManagerService.releaseKey(currentApiKey.id);

          // 记录错误但继续尝试其他key
          this.logger.warn(
            `Key ${currentApiKey.id} failed (${errorClassification.type}), trying next key`,
          );

          // 如果是限流错误，等待一段时间
          if (errorClassification.retryDelayMs > 0 && errorClassification.type === OpenAIErrorType.RATE_LIMIT) {
            const waitTime = Math.min(errorClassification.retryDelayMs, 10000); // 最多等待 10 秒
            this.logger.log(`Rate limited, waiting ${waitTime}ms before trying next key`);
            await this.sleep(waitTime);
          }

          // 如果是速率限制，稍等一下再尝试下一个key
          if (lastError.type === 'rate_limit') {
            const waitTime = this.configService.get<number>(
              'rateLimit.retryAfterMs',
              5000, // 减少等待时间到5秒
            );
            this.logger.log(`Rate limited, waiting ${waitTime}ms before trying next key`);
            await this.sleep(waitTime);
          }

          // 继续尝试下一个key
          currentApiKey = null; // 重置当前key，让循环获取新的key
        }
      }
    } finally {
      // Always release the key when done (success or failure)
      if (currentApiKey) {
        await this.keyManagerService.releaseKey(currentApiKey.id);
        this.logger.debug(`Released key ${currentApiKey.id}`);
      }
    }

    // If we get here, all key attempts failed
    throw new Error(
      `Request failed after trying ${maxKeyAttempts} different keys. Last error: ${lastError?.message || 'Unknown error'}`,
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  getProvider(modelType?: string): BaseModelProvider | undefined {
    return this.providers.get(modelType || this.modelType);
  }

  getSupportedProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  getCurrentModelType(): string {
    return this.modelType;
  }

  async getProviderStatus(): Promise<Record<string, any>> {
    const provider = this.providers.get(this.modelType);
    const keyStatus = this.keyManagerService.getKeyPoolStatus();
    const hasKeys = await this.keyManagerService.hasAvailableKeys();

    return {
      modelType: this.modelType,
      providerName: provider?.providerName || 'unknown',
      supportedModels: provider?.getSupportedModels?.() || [],
      keyPoolStatus: keyStatus,
      hasAvailableKeys: hasKeys,
    };
  }

  validateRequest(request: ChatCompletionRequest): {
    valid: boolean;
    error?: string;
  } {
    if (!request.model) {
      return { valid: false, error: 'Model is required' };
    }

    if (!request.messages || request.messages.length === 0) {
      return { valid: false, error: 'Messages are required' };
    }

    const provider = this.providers.get(this.modelType);
    if (provider?.validateModel && !provider.validateModel(request.model)) {
      return {
        valid: false,
        error: `Model ${request.model} is not supported by provider ${this.modelType}`,
      };
    }

    return { valid: true };
  }



  /**
   * Update key status based on error type and classification
   */
  private async updateKeyStatusOnError(keyId: string, error: ProviderError, classification?: any): Promise<void> {
    try {
      // Use KeyManagerService to handle the error and update memory status
      await this.keyManagerService.handleApiError(keyId, error);

      // 如果有分类信息，记录详细信息
      if (classification) {
        this.logger.debug(`Updated key ${keyId} status based on ${classification.type}: ${classification.description}`);
      }
    } catch (updateError) {
      this.logger.error(
        `Failed to update key ${keyId} status: ${updateError instanceof Error ? updateError.message : String(updateError)}`,
      );
    }
  }


}
