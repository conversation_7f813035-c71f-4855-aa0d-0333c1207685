export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ProviderError {
  code: string;
  message: string;
  type:
    | 'rate_limit'
    | 'insufficient_quota'
    | 'invalid_request'
    | 'server_error'
    | 'unknown';
  retryable: boolean;
}

export abstract class BaseModelProvider {
  abstract readonly providerName: string;

  abstract chatCompletion(
    request: ChatCompletionRequest,
    apiKey: string,
  ): Promise<ChatCompletionResponse | AsyncIterable<string>>;

  // Optional methods that providers can implement
  getSupportedModels?(): string[];
  validateModel?(model: string): boolean;

  protected handleError(error: unknown): ProviderError {
    // Default error handling - can be overridden by specific providers
    const errorObj = error as {
      response?: { status: number; data?: { error?: { message?: string } } };
    };
    if (errorObj.response) {
      const status = errorObj.response.status;
      const data = errorObj.response.data;

      switch (status) {
        case 429:
          return {
            code: 'rate_limit_exceeded',
            message: data?.error?.message || 'Rate limit exceeded',
            type: 'rate_limit',
            retryable: true,
          };
        case 403:
          return {
            code: 'insufficient_quota',
            message: data?.error?.message || 'Insufficient quota',
            type: 'insufficient_quota',
            retryable: false,
          };
        case 400:
          return {
            code: 'invalid_request',
            message: data?.error?.message || 'Invalid request',
            type: 'invalid_request',
            retryable: false,
          };
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            code: 'server_error',
            message: data?.error?.message || 'Server error',
            type: 'server_error',
            retryable: true,
          };
        default:
          return {
            code: 'unknown_error',
            message:
              data?.error?.message ||
              (errorObj instanceof Error
                ? errorObj.message
                : JSON.stringify(errorObj)) ||
              'Unknown error',
            type: 'unknown',
            retryable: false,
          };
      }
    }

    return {
      code: 'network_error',
      message:
        (errorObj instanceof Error
          ? errorObj.message
          : JSON.stringify(errorObj)) || 'Network error',
      type: 'unknown',
      retryable: true,
    };
  }
}
