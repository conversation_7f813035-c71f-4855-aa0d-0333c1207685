# 🔐 SightAI Executor 密钥配置完整指南

## 📋 概述

SightAI Executor 支持两种密钥管理模式：
- **开发环境**: 使用本地私钥 (存储在环境变量中)
- **生产环境**: 使用 GCP Secret Manager (安全存储)

系统会按以下优先级加载密钥：
1. **GCP Secret Manager** (如果配置了 `GCP_KMS_PROJECT_ID` 和 `EXECUTOR_SECRET_ID`)
2. **环境变量** (`EXECUTOR_PRIVATE_KEY`)
3. **生成临时密钥** (仅开发模式)

---

## 🚀 快速开始

### 方式一：一键配置 (推荐)

```bash
# 1. 设置 GCP 项目 ID
export GCP_KMS_PROJECT_ID=your-gcp-project-id

# 2. 开发环境配置
node scripts/setup-environment.js dev

# 3. 启动开发服务
npm run start:dev
```

### 方式二：生产环境配置

```bash
# 1. 设置 GCP 认证
export GOOGLE_APPLICATION_CREDENTIALS=./gcp-service-account.json
export GCP_KMS_PROJECT_ID=your-gcp-project-id

# 2. 配置生产环境
node scripts/setup-environment.js prod

# 3. 部署到生产环境
# 复制 .env.production 到生产服务器
# 启动: npm run start:prod
```

---

## 🔧 详细配置步骤

### 1. 开发环境配置

#### 步骤 1: 生成密钥对
```bash
cd sight-executor
node scripts/generate-executor-keypair.js
```

**输出文件**:
- `keys/executor-keypair.json` - 完整密钥对 (用于 GCP)
- `keys/executor-public-key.json` - 仅公钥
- `keys/executor-private-key.env` - 环境变量格式

#### 步骤 2: 配置开发环境
```bash
# 复制示例配置
cp .env.example .env

# 添加私钥到环境变量
cat keys/executor-private-key.env >> .env
```

#### 步骤 3: 启动开发服务
```bash
npm run start:dev
```

### 2. 生产环境配置

#### 步骤 1: 准备 GCP 认证
```bash
# 下载服务账号密钥文件
# 放置在 sight-executor/gcp-service-account.json

# 或设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
export GCP_KMS_PROJECT_ID=your-gcp-project-id
```

#### 步骤 2: 上传密钥到 GCP
```bash
# 确保已生成密钥对
node scripts/generate-executor-keypair.js

# 上传到 GCP Secret Manager
node scripts/upload-to-gcp.js --project-id your-gcp-project-id
```

#### 步骤 3: 配置生产环境变量
```bash
# 创建生产环境配置
cat > .env.production << EOF
NODE_ENV=production
EXECUTOR_ID=executor-prod-001
EXECUTOR_REGION=asia
EXECUTOR_MODEL_TYPE=openai
EXECUTOR_PORT=3002

# etcd 配置
ETCD_ENDPOINTS=etcd-cluster:2379

# GCP 配置
GCP_KMS_PROJECT_ID=your-gcp-project-id
EXECUTOR_SECRET_ID=executor-keypair-001

# 不设置 EXECUTOR_PRIVATE_KEY，让系统从 GCP 自动加载
EOF
```

#### 步骤 4: 配置 GCP 权限
```bash
# 为服务账号添加 Secret Manager 访问权限
gcloud secrets add-iam-policy-binding executor-keypair-001 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```