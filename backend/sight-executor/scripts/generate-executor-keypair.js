#!/usr/bin/env node

/**
 * 🔐 Executor 密钥对生成脚本
 *
 * 功能：为 Executor 生成 X25519 密钥对，用于 API Key 加密/解密
 *
 * 生成内容：
 * 1. X25519 密钥对 (公钥 + 私钥)
 * 2. 输出多种格式的密钥文件
 * 3. 自动创建 keys 目录
 *
 * 输出文件：
 * - keys/executor-keypair.json (完整密钥对，用于 GCP Secret Manager)
 * - keys/executor-public-key.json (仅公钥)
 * - keys/executor-private-key.env (环境变量格式)
 *
 * 使用方法：
 * node scripts/generate-executor-keypair.js
 *
 * 注意：生成的私钥应该安全存储在 GCP Secret Manager 中
 */

const nacl = require('tweetnacl');
const fs = require('fs');
const path = require('path');

function generateKeyPair() {
  console.log('🔐 Generating X25519 key pair for Executor...');
  
  // Generate X25519 key pair using NaCl
  const keyPair = nacl.box.keyPair();
  
  const executorKeyPair = {
    keyId: `executor-${Date.now()}`,
    publicKey: Buffer.from(keyPair.publicKey).toString('base64'),
    privateKey: Buffer.from(keyPair.secretKey).toString('base64'),
    createdAt: new Date().toISOString(),
    algorithm: 'X25519',
    purpose: 'API Key Encryption/Decryption'
  };
  
  return executorKeyPair;
}

function saveKeyPair(keyPair, outputDir = './keys') {
  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Save complete key pair (for GCP Secret Manager)
  const keypairPath = path.join(outputDir, 'executor-keypair.json');
  fs.writeFileSync(keypairPath, JSON.stringify(keyPair, null, 2));
  
  // Save public key only (for reference)
  const publicKeyPath = path.join(outputDir, 'executor-public-key.json');
  const publicKeyData = {
    keyId: keyPair.keyId,
    publicKey: keyPair.publicKey,
    createdAt: keyPair.createdAt
  };
  fs.writeFileSync(publicKeyPath, JSON.stringify(publicKeyData, null, 2));
  
  // Save private key for environment variable (development fallback)
  const envPath = path.join(outputDir, 'executor-private-key.env');
  fs.writeFileSync(envPath, `EXECUTOR_PRIVATE_KEY=${keyPair.privateKey}\n`);
  
  return {
    keypairPath,
    publicKeyPath,
    envPath
  };
}

function main() {
  try {
    const keyPair = generateKeyPair();
    const paths = saveKeyPair(keyPair);
    
    console.log('✅ Key pair generated successfully!');
    console.log('');
    console.log('📁 Files created:');
    console.log(`   Complete key pair: ${paths.keypairPath}`);
    console.log(`   Public key only:   ${paths.publicKeyPath}`);
    console.log(`   Environment file:  ${paths.envPath}`);
    console.log('');
    console.log('🔑 Key Information:');
    console.log(`   Key ID:     ${keyPair.keyId}`);
    console.log(`   Public Key: ${keyPair.publicKey}`);
    console.log(`   Created:    ${keyPair.createdAt}`);
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Store the complete key pair in GCP Secret Manager:');
    console.log(`   gcloud secrets create executor-keypair-001 --data-file="${paths.keypairPath}"`);
    console.log('');
    console.log('2. For development, add to your .env file:');
    console.log(`   cat ${paths.envPath} >> sight-executor/.env`);
    console.log('');
    console.log('3. Set up GCP permissions:');
    console.log('   gcloud secrets add-iam-policy-binding executor-keypair-001 \\');
    console.log('     --member="serviceAccount:<EMAIL>" \\');
    console.log('     --role="roles/secretmanager.secretAccessor"');
    
  } catch (error) {
    console.error('❌ Error generating key pair:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { generateKeyPair, saveKeyPair };
