#!/usr/bin/env node

/**
 * 🚀 上传 Executor 密钥对到 GCP Secret Manager
 * 
 * 功能：
 * 1. 读取本地生成的密钥对文件
 * 2. 上传到 GCP Secret Manager
 * 3. 验证上传结果
 * 
 * 使用方法：
 * node scripts/upload-to-gcp.js [options]
 * 
 * 选项：
 * --project-id <id>     GCP 项目 ID
 * --secret-id <id>      Secret ID (默认: executor-keypair-001)
 * --key-file <path>     密钥文件路径 (默认: keys/executor-keypair.json)
 * --force               强制覆盖已存在的 Secret
 */

const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const fs = require('fs');
const path = require('path');

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    projectId: process.env.GCP_KMS_PROJECT_ID,
    secretId: 'executor-keypair-001',
    keyFile: 'keys/executor-keypair.json',
    force: false,
  };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--project-id':
        options.projectId = args[++i];
        break;
      case '--secret-id':
        options.secretId = args[++i];
        break;
      case '--key-file':
        options.keyFile = args[++i];
        break;
      case '--force':
        options.force = true;
        break;
      case '--help':
        console.log(`
🚀 上传 Executor 密钥对到 GCP Secret Manager

使用方法: node scripts/upload-to-gcp.js [options]

选项:
  --project-id <id>     GCP 项目 ID
  --secret-id <id>      Secret ID (默认: executor-keypair-001)
  --key-file <path>     密钥文件路径 (默认: keys/executor-keypair.json)
  --force               强制覆盖已存在的 Secret
  --help                显示帮助信息

环境变量:
  GCP_KMS_PROJECT_ID              GCP 项目 ID
  GOOGLE_APPLICATION_CREDENTIALS  GCP 服务账号密钥文件路径

示例:
  # 基本使用
  node scripts/upload-to-gcp.js --project-id my-project

  # 自定义 Secret ID
  node scripts/upload-to-gcp.js --project-id my-project --secret-id executor-keypair-prod

  # 强制覆盖
  node scripts/upload-to-gcp.js --project-id my-project --force
        `);
        process.exit(0);
        break;
    }
  }

  return options;
}

// 验证参数
function validateOptions(options) {
  if (!options.projectId) {
    console.error('❌ 错误: 缺少 GCP 项目 ID');
    console.error('请通过 --project-id 参数或 GCP_KMS_PROJECT_ID 环境变量提供');
    process.exit(1);
  }

  const keyFilePath = path.resolve(options.keyFile);
  if (!fs.existsSync(keyFilePath)) {
    console.error(`❌ 错误: 密钥文件不存在: ${keyFilePath}`);
    console.error('请先运行 generate-executor-keypair.js 生成密钥对');
    process.exit(1);
  }

  options.keyFile = keyFilePath;
  return options;
}

// 加载密钥对文件
function loadKeyPair(keyFilePath) {
  try {
    const keyData = JSON.parse(fs.readFileSync(keyFilePath, 'utf8'));
    
    if (!keyData.privateKey || !keyData.publicKey || !keyData.keyId) {
      throw new Error('密钥文件格式无效: 缺少必要字段');
    }

    return keyData;
  } catch (error) {
    console.error(`❌ 错误: 无法加载密钥文件: ${error.message}`);
    process.exit(1);
  }
}

// 上传到 GCP Secret Manager
async function uploadToGCP(options, keyData) {
  const client = new SecretManagerServiceClient();
  
  try {
    console.log(`📤 正在上传密钥对到 GCP Secret Manager...`);
    console.log(`   项目 ID: ${options.projectId}`);
    console.log(`   Secret ID: ${options.secretId}`);
    console.log(`   密钥 ID: ${keyData.keyId}`);

    // 检查 Secret 是否存在
    let secretExists = false;
    try {
      await client.getSecret({
        name: `projects/${options.projectId}/secrets/${options.secretId}`,
      });
      secretExists = true;
      console.log(`⚠️  Secret ${options.secretId} 已存在`);
      
      if (!options.force) {
        console.error('❌ 使用 --force 参数强制覆盖');
        process.exit(1);
      }
    } catch (error) {
      // Secret 不存在，需要创建
      console.log(`📝 创建新的 Secret: ${options.secretId}`);
    }

    // 创建 Secret (如果不存在)
    if (!secretExists) {
      await client.createSecret({
        parent: `projects/${options.projectId}`,
        secretId: options.secretId,
        secret: {
          replication: {
            automatic: {},
          },
        },
      });
      console.log(`✅ Secret 创建成功: ${options.secretId}`);
    }

    // 添加新版本
    const [version] = await client.addSecretVersion({
      parent: `projects/${options.projectId}/secrets/${options.secretId}`,
      payload: {
        data: Buffer.from(JSON.stringify(keyData)),
      },
    });

    console.log(`✅ 密钥对上传成功!`);
    console.log(`   版本: ${version.name}`);
    console.log(`   创建时间: ${version.createTime?.seconds ? new Date(version.createTime.seconds * 1000).toISOString() : 'Unknown'}`);

  } catch (error) {
    console.error(`❌ 上传失败: ${error.message}`);
    
    if (error.code === 7) {
      console.error('💡 提示: 请检查 GCP 认证和权限配置');
      console.error('   - 确保已设置 GOOGLE_APPLICATION_CREDENTIALS 环境变量');
      console.error('   - 确保服务账号有 Secret Manager 的读写权限');
    }
    
    process.exit(1);
  }
}

// 验证上传结果
async function verifyUpload(options) {
  const client = new SecretManagerServiceClient();

  try {
    console.log(`🔍 验证上传结果...`);
    
    const [version] = await client.accessSecretVersion({
      name: `projects/${options.projectId}/secrets/${options.secretId}/versions/latest`,
    });

    const secretData = version.payload?.data?.toString();
    if (!secretData) {
      throw new Error('Secret 数据为空');
    }

    const keyData = JSON.parse(secretData);
    
    if (!keyData.privateKey || !keyData.publicKey || !keyData.keyId) {
      throw new Error('Secret 数据格式无效');
    }

    console.log(`✅ 验证成功!`);
    console.log(`   密钥 ID: ${keyData.keyId}`);
    console.log(`   创建时间: ${keyData.createdAt}`);
    console.log(`   算法: ${keyData.algorithm}`);

  } catch (error) {
    console.error(`❌ 验证失败: ${error.message}`);
    process.exit(1);
  }
}

// 显示使用说明
function showUsageInstructions(options) {
  console.log(`\n📋 使用说明:`);
  console.log(`\n🔧 开发环境配置:`);
  console.log(`   在 .env 文件中设置:`);
  console.log(`   GCP_KMS_PROJECT_ID=${options.projectId}`);
  console.log(`   EXECUTOR_SECRET_ID=${options.secretId}`);
  console.log(`   # 开发环境可以同时设置 EXECUTOR_PRIVATE_KEY 作为备用`);
  
  console.log(`\n🚀 生产环境配置:`);
  console.log(`   在生产环境的环境变量中设置:`);
  console.log(`   GCP_KMS_PROJECT_ID=${options.projectId}`);
  console.log(`   EXECUTOR_SECRET_ID=${options.secretId}`);
  console.log(`   # 不要设置 EXECUTOR_PRIVATE_KEY，让系统自动从 GCP 加载`);
  
  console.log(`\n🔐 GCP 权限配置:`);
  console.log(`   gcloud secrets add-iam-policy-binding ${options.secretId} \\`);
  console.log(`     --member="serviceAccount:<EMAIL>" \\`);
  console.log(`     --role="roles/secretmanager.secretAccessor"`);
  
  console.log(`\n🧪 测试配置:`);
  console.log(`   node -e "console.log(require('./libs/secret-manager/src/secret-manager.service.js'))"`);
}

// 主函数
async function main() {
  console.log('🔐 SightAI Executor 密钥对上传工具\n');

  const options = parseArgs();
  validateOptions(options);
  
  const keyData = loadKeyPair(options.keyFile);
  
  await uploadToGCP(options, keyData);
  await verifyUpload(options);
  
  showUsageInstructions(options);
  
  console.log('\n🎉 密钥对上传完成!');
}

// 运行脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  });
}
