# Executor 脚本工具

这个目录包含了用于管理 Executor 密钥和配置的部署脚本。

## 📁 脚本列表

### 🛠️ 环境配置脚本

#### `complete-setup.js` ⭐ **推荐使用**
完整的 Executor 环境配置工具，包含 GCP 认证和权限管理。

**功能**：
- 检查 GCP 认证和权限
- 生成 X25519 密钥对
- 上传到 GCP Secret Manager
- 配置开发/生产环境
- 验证配置完整性

**使用方法**：
```bash
# 检查当前配置状态
node complete-setup.js check

# 配置开发环境
node complete-setup.js dev

# 配置生产环境 (包含上传到 GCP)
node complete-setup.js prod
```

#### `setup-environment.js`
旧版本的环境配置工具。

**使用方法**：
```bash
# 开发环境 (使用本地密钥)
node setup-environment.js dev

# 生产环境 (上传到 GCP)
node setup-environment.js prod

# 同时配置开发和生产环境
node setup-environment.js both
```

### 🔐 密钥管理脚本

#### `generate-executor-keypair.js`
生成 Executor 的 X25519 密钥对。

**功能**：
- 生成新的 X25519 密钥对
- 保存为多种格式的文件
- 提供 GCP 上传指令

**使用方法**：
```bash
node generate-executor-keypair.js
```

#### `upload-to-gcp.js`
上传本地密钥对到 GCP Secret Manager。

**功能**：
- 读取本地密钥对文件
- 上传到 GCP Secret Manager
- 验证上传结果

**使用方法**：
```bash
node upload-to-gcp.js --project-id your-project-id
```

#### `generate-executor-secret.js`
为 Executor 生成和管理密钥 (旧版本)。

**使用方法**：
```bash
node generate-executor-secret.js
```

## 🚀 快速开始

### 统一配置脚本 (推荐) ⭐

```bash
# 开发环境 - 不依赖 GCP (默认)
node scripts/setup-environment.js dev

# 生产环境 - 使用 GCP
node scripts/setup-environment.js prod

# 验证配置
node scripts/setup-environment.js verify

# 测试连接
node scripts/setup-environment.js test

# 清理配置
node scripts/setup-environment.js clean
```

### 方式二：分步配置

#### 1. 生成密钥对
```bash
node generate-executor-keypair.js
```

#### 2. 上传到 GCP (生产环境)
```bash
node upload-to-gcp.js --project-id your-project-id
```

#### 3. 配置环境变量
```bash
# 开发环境：复制私钥到 .env
cat keys/executor-private-key.env >> .env

# 生产环境：设置 GCP 配置
echo "GCP_KMS_PROJECT_ID=your-project-id" >> .env
echo "EXECUTOR_SECRET_ID=executor-keypair-001" >> .env
```

## 📁 脚本说明

### 🔧 主要脚本

1. **`setup-environment.js`** ⭐ **统一配置脚本**
   - 生成密钥对
   - 配置开发/生产环境
   - 同步 Gateway 认证
   - 验证配置完整性
   - 测试系统连接
   - 清理配置文件

### 🔧 辅助脚本

2. **`generate-executor-keypair.js`** - 密钥对生成
   - 生成 X25519 密钥对
   - 保存到 keys/ 目录
   - 创建环境变量文件

3. **`upload-to-gcp.js`** - GCP 上传工具
   - 上传密钥到 Secret Manager
   - 配置 GCP 项目
   - 验证上传结果

## 📝 部署验证清单

### ✅ 环境准备
- [ ] GCP 项目配置正确
- [ ] Secret Manager API 已启用
- [ ] etcd 服务运行正常
- [ ] 网络连接正常

### ✅ 密钥生成验证
- [ ] X25519 密钥对生成成功
- [ ] 私钥存储到 GCP Secret Manager
- [ ] 公钥注册到 etcd
- [ ] Executor 配置更新

## 🔧 故障排除

### GCP 认证失败
```bash
# 检查认证状态
gcloud auth list

# 重新认证
gcloud auth application-default login

# 检查项目权限
gcloud projects get-iam-policy PROJECT_ID
```

### etcd 连接失败
```bash
# 检查 etcd 状态
etcdctl endpoint health

# 测试连接
etcdctl put test-key test-value
etcdctl get test-key
etcdctl del test-key
```

### 密钥生成失败
```bash
# 检查 Node.js 版本 (需要 18+)
node --version

# 检查依赖
npm list tweetnacl @noble/ciphers

# 重新安装依赖
npm install
```

## 📚 相关文档

- [部署指南](../../docs/Deployment-Guide.md)
- [系统架构](../../docs/SightAI-System-Architecture.md)
- [技术实现细节](../../docs/Technical-Implementation-Details.md)
