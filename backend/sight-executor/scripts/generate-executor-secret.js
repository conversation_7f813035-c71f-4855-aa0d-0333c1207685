#!/usr/bin/env node

/**
 * 🔑 Executor 认证密钥生成脚本
 *
 * 功能：生成 Executor 与 Gateway 通信时使用的认证密钥
 *
 * 工作原理：
 * 1. 使用 executorId、region、modelType、secretKey 生成 SHA256 哈希
 * 2. 截取前 32 个字符作为认证密钥
 * 3. 与 ExecutorAuthGuard.generateExecutorSecretStatic() 逻辑一致
 *
 * 使用方法：
 * node scripts/generate-executor-secret.js [executorId] [region] [modelType] [secretKey]
 *
 * 示例：
 * node scripts/generate-executor-secret.js executor-001 asia openai default-secret-key
 *
 * 默认参数：
 * - executorId: executor-001
 * - region: asia
 * - modelType: openai
 * - secretKey: default-secret-key
 */

const crypto = require('crypto');
function generateExecutorSecret(executorId, region, modelType, secretKey = 'default-secret-key') {
  const payload = `${executorId}:${region}:${modelType}:${secretKey}`;
  return crypto.createHash('sha256').update(payload).digest('hex').substring(0, 32);
}

// Get parameters from command line or use defaults
const executorId = process.argv[2] || 'executor-001';
const region = process.argv[3] || 'asia';
const modelType = process.argv[4] || 'openai';
const secretKey = process.argv[5] || 'default-secret-key';

console.log('🔐 Generating Executor Secret...');
console.log(`   Executor ID: ${executorId}`);
console.log(`   Region: ${region}`);
console.log(`   Model Type: ${modelType}`);
console.log(`   Secret Key: ${secretKey}`);
console.log('');

const secret = generateExecutorSecret(executorId, region, modelType, secretKey);

console.log(`✅ Generated Secret: ${secret}`);
console.log('');
console.log('📋 Use this in your curl request:');
console.log(`   -H "x-executor-secret: ${secret}"`);
console.log('');
console.log('🧪 Complete curl command:');
console.log(`curl -X GET "http://localhost:8718/executor/key?provider=openai&region=asia" \\`);
console.log(`  -H "Content-Type: application/json" \\`);
console.log(`  -H "x-executor-region: ${region}" \\`);
console.log(`  -H "x-executor-model-type: ${modelType}" \\`);
console.log(`  -H "x-executor-id: ${executorId}" \\`);
console.log(`  -H "x-executor-secret: ${secret}"`);
