{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/sight-executor/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/sight-executor/tsconfig.app.json"}, "monorepo": true, "root": "apps/sight-executor", "projects": {"sight-executor": {"type": "application", "root": "apps/sight-executor", "entryFile": "main", "sourceRoot": "apps/sight-executor/src", "compilerOptions": {"tsConfigPath": "apps/sight-executor/tsconfig.app.json"}}, "etcd": {"type": "library", "root": "libs/etcd", "entryFile": "index", "sourceRoot": "libs/etcd/src", "compilerOptions": {"tsConfigPath": "libs/etcd/tsconfig.lib.json"}}, "key-manager": {"type": "library", "root": "libs/key-manager", "entryFile": "index", "sourceRoot": "libs/key-manager/src", "compilerOptions": {"tsConfigPath": "libs/key-manager/tsconfig.lib.json"}}, "providers": {"type": "library", "root": "libs/providers", "entryFile": "index", "sourceRoot": "libs/providers/src", "compilerOptions": {"tsConfigPath": "libs/providers/tsconfig.lib.json"}}, "executor-registry": {"type": "library", "root": "libs/executor-registry", "entryFile": "index", "sourceRoot": "libs/executor-registry/src", "compilerOptions": {"tsConfigPath": "libs/executor-registry/tsconfig.lib.json"}}, "config": {"type": "library", "root": "libs/config", "entryFile": "index", "sourceRoot": "libs/config/src", "compilerOptions": {"tsConfigPath": "libs/config/tsconfig.lib.json"}}, "types": {"type": "library", "root": "libs/types", "entryFile": "index", "sourceRoot": "libs/types/src", "compilerOptions": {"tsConfigPath": "libs/types/tsconfig.lib.json"}}}}