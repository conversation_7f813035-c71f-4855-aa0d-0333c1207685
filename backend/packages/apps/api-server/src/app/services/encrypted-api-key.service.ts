import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';
import { v4 as uuidv4 } from 'uuid';
import { PublicKeyService } from './public-key.service';
import { ApiKeyRepository } from '@saito/apikey';

export interface CreateEncryptedApiKeyRequest {
  provider: string;
  region: string;
  keyId: string;                  // 目标 Executor 的 keyId
  encryptedKey: string;          // base64(ciphertext) 加密后的Provider API KEY
  nonce: string;                 // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
  tag: string;                   // base64(tag) ChaCha20-Poly1305要求解密用
  ephemeralPubKey: string;       // base64(X25519 公钥)
}

export interface EncryptedApiKeyData {
  keyId: string;                  // 目标 Executor 的 keyId
  encryptedKey: string;          // base64(ciphertext) 加密后的Provider API KEY
  nonce: string;                 // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
  tag: string;                   // base64(tag) ChaCha20-Poly1305要求解密用
  ephemeralPubKey: string;       // base64(X25519 公钥)
  status: 'active' | 'revoked' | 'waiting-to-verify';
  createdAt: string;             // ISO8601 string
  createdBy: string;             // User ID who created this key
}

export interface CreateEncryptedApiKeyResult {
  uuid: string;
  provider: string;
  region: string;
  keyId: string;
  status: string;
  createdAt: string;
}

@Injectable()
export class EncryptedApiKeyService {
  private readonly logger = new Logger(EncryptedApiKeyService.name);
  private etcdClient!: Etcd3;

  constructor(
    private configService: ConfigService,
    private publicKeyService: PublicKeyService,
    private apiKeyRepository: ApiKeyRepository,
  ) {
    this.initializeEtcdClient();
  }

  private initializeEtcdClient(): void {
    const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
    const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);

    this.etcdClient = new Etcd3({
      hosts: [`${etcdHost}:${etcdPort}`],
    });

    this.logger.log(`EncryptedApiKeyService connected to etcd at ${etcdHost}:${etcdPort}`);
  }

  /**
   * Create and store encrypted API key in etcd
   * Path: /api-keys/{provider}/{region}/{uuid}
   */
  async createEncryptedApiKey(
    request: CreateEncryptedApiKeyRequest,
    userId: string,
  ): Promise<CreateEncryptedApiKeyResult> {
    try {
      // Validate that the target executor exists and is active
      const isExecutorAvailable = await this.publicKeyService.isExecutorAvailable(
        request.provider,
        request.region,
        request.keyId,
      );

      if (!isExecutorAvailable) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: `Target executor not found or inactive: ${request.keyId}`,
              code: 'EXECUTOR_NOT_AVAILABLE',
            },
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Validate encryption data format (basic validation)
      if (!this.isValidBase64(request.encryptedKey) ||
          !this.isValidBase64(request.nonce) ||
          !this.isValidBase64(request.tag) ||
          !this.isValidBase64(request.ephemeralPubKey)) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Invalid base64 encoding in encryption data',
              code: 'INVALID_ENCRYPTION_DATA',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generate UUID for this encrypted key
      const uuid = uuidv4();
      const keyPath = `/api-keys/${request.provider}/${request.region}/${uuid}`;

      // Create database record first
      const dbRecord = await this.apiKeyRepository.createEncryptedApiKey({
        userId,
        uuid,
        provider: request.provider,
        region: request.region,
        encryptedKey: request.encryptedKey,
        name: `Encrypted ${request.provider} key`,
        description: `Encrypted API key for ${request.provider} in ${request.region}`,
      });

      // Prepare encrypted key data for etcd
      const encryptedKeyData: EncryptedApiKeyData = {
        keyId: request.keyId,
        encryptedKey: request.encryptedKey,
        nonce: request.nonce,
        tag: request.tag,
        ephemeralPubKey: request.ephemeralPubKey,
        status: 'waiting-to-verify', // Initial status
        createdAt: new Date().toISOString(),
        createdBy: userId,
      };

      // Store in etcd
      await this.etcdClient.put(keyPath).value(JSON.stringify(encryptedKeyData));

      this.logger.log(
        `Created encrypted API key: ${uuid} for provider: ${request.provider}, region: ${request.region}, executor: ${request.keyId}`,
      );

      // Return result
      const result: CreateEncryptedApiKeyResult = {
        uuid,
        provider: request.provider,
        region: request.region,
        keyId: request.keyId,
        status: encryptedKeyData.status,
        createdAt: encryptedKeyData.createdAt,
      };

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to create encrypted API key: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException(
        {
          success: false,
          error: {
            message: 'Internal server error while creating encrypted API key',
            code: 'INTERNAL_ERROR',
          },
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get encrypted API key by UUID
   */
  async getEncryptedApiKey(
    provider: string,
    region: string,
    uuid: string,
  ): Promise<EncryptedApiKeyData | null> {
    try {
      const keyPath = `/api-keys/${provider}/${region}/${uuid}`;
      const jsonValue = await this.etcdClient.get(keyPath);

      if (!jsonValue) {
        return null;
      }

      return JSON.parse(jsonValue) as EncryptedApiKeyData;
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted API key ${uuid}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Update encrypted API key status
   */
  async updateEncryptedApiKeyStatus(
    provider: string,
    region: string,
    uuid: string,
    status: 'active' | 'revoked' | 'waiting-to-verify',
    userId?: string,
  ): Promise<boolean> {
    try {
      const keyPath = `/api-keys/${provider}/${region}/${uuid}`;

      // Update database record first
      const dbStatus = status === 'waiting-to-verify' ? 'waiting-to-verify' :
                      status === 'revoked' ? 'revoked' : 'active';

      const updatedDbRecord = await this.apiKeyRepository.updateEncryptedApiKeyStatus(
        uuid,
        dbStatus as 'active' | 'inactive' | 'revoked' | 'waiting-to-verify',
        userId
      );

      if (!updatedDbRecord) {
        this.logger.warn(`Cannot update non-existent encrypted API key in database: ${uuid}`);
        return false;
      }

      // Get existing etcd data
      const existingData = await this.getEncryptedApiKey(provider, region, uuid);
      if (!existingData) {
        this.logger.warn(`Cannot update non-existent encrypted API key in etcd: ${uuid}`);
        return false;
      }

      // Update etcd status
      const updatedData = {
        ...existingData,
        status,
      };

      await this.etcdClient.put(keyPath).value(JSON.stringify(updatedData));

      this.logger.log(`Updated encrypted API key ${uuid} status to: ${status} in both database and etcd`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to update encrypted API key status for ${uuid}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * List encrypted API keys for a user from database
   */
  async listEncryptedApiKeysFromDatabase(
    userId: string,
    options?: {
      provider?: string;
      region?: string;
      status?: string;
      page?: number;
      pageSize?: number;
    }
  ): Promise<{
    items: Array<{
      id: string;
      name: string;
      provider: string;
      status: string;
      createdAt: string;
      updatedAt: string;
      totalRequests: number;
      totalTokens: number;
      totalCost: number;
    }>;
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const result = await this.apiKeyRepository.getEncryptedApiKeys(userId, options);

      return {
        items: result.items.map(item => ({
          id: item.id,
          name: item.name || 'Encrypted API Key',
          provider: item.provider || 'unknown',
          status: item.status,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          totalRequests: item.totalRequests || 0,
          totalTokens: item.totalTokens || 0,
          totalCost: item.totalCost || 0,
        })),
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
      };
    } catch (error) {
      this.logger.error(
        `Failed to list encrypted API keys from database: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * List encrypted API keys for a provider/region from etcd
   */
  async listEncryptedApiKeys(
    provider: string,
    region?: string,
    status?: 'active' | 'revoked' | 'waiting-to-verify',
  ): Promise<{
    keys: Array<EncryptedApiKeyData & { uuid: string }>;
    totalCount: number;
  }> {
    try {
      let keyPrefix: string;
      
      if (region) {
        keyPrefix = `/api-keys/${provider}/${region}/`;
      } else {
        keyPrefix = `/api-keys/${provider}/`;
      }

      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
      const keys: Array<EncryptedApiKeyData & { uuid: string }> = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string) as EncryptedApiKeyData;
          
          // Apply status filter if provided
          if (status && keyData.status !== status) {
            continue;
          }

          // Extract UUID from path
          const pathParts = fullPath.split('/');
          const uuid = pathParts[pathParts.length - 1];

          keys.push({
            ...keyData,
            uuid,
          });
        } catch (parseError) {
          this.logger.warn(`Failed to parse encrypted API key data for ${fullPath}: ${parseError}`);
        }
      }

      // Sort by creation time, newest first
      keys.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      return {
        keys,
        totalCount: keys.length,
      };
    } catch (error) {
      this.logger.error(
        `Failed to list encrypted API keys: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Delete encrypted API key
   */
  async deleteEncryptedApiKey(provider: string, region: string, uuid: string, userId: string): Promise<boolean> {
    try {
      // Delete from database first (soft delete)
      const dbDeleted = await this.apiKeyRepository.deleteEncryptedApiKey(uuid, userId);

      if (!dbDeleted) {
        this.logger.warn(`Cannot delete non-existent encrypted API key from database: ${uuid}`);
        return false;
      }

      // Delete from etcd
      const keyPath = `/api-keys/${provider}/${region}/${uuid}`;
      await this.etcdClient.delete().key(keyPath);

      this.logger.log(`Deleted encrypted API key: ${uuid} from both database and etcd`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to delete encrypted API key ${uuid}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }

  /**
   * Validate base64 encoding
   */
  private isValidBase64(str: string): boolean {
    try {
      return Buffer.from(str, 'base64').toString('base64') === str;
    } catch {
      return false;
    }
  }

  /**
   * Get statistics about encrypted API keys
   */
  async getEncryptedApiKeyStatistics(): Promise<{
    totalKeys: number;
    keysByStatus: Record<string, number>;
    keysByProvider: Record<string, number>;
    keysByRegion: Record<string, number>;
  }> {
    try {
      const keyPrefix = '/api-keys/';
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);

      let totalKeys = 0;
      const keysByStatus: Record<string, number> = {};
      const keysByProvider: Record<string, number> = {};
      const keysByRegion: Record<string, number> = {};

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string) as EncryptedApiKeyData;
          
          totalKeys++;
          
          // Count by status
          keysByStatus[keyData.status] = (keysByStatus[keyData.status] || 0) + 1;

          // Extract provider and region from path
          const pathParts = fullPath.split('/');
          if (pathParts.length >= 4) {
            const provider = pathParts[2];
            const region = pathParts[3];
            
            keysByProvider[provider] = (keysByProvider[provider] || 0) + 1;
            keysByRegion[region] = (keysByRegion[region] || 0) + 1;
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse encrypted API key data for statistics: ${fullPath}`);
        }
      }

      return {
        totalKeys,
        keysByStatus,
        keysByProvider,
        keysByRegion,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted API key statistics: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }
}
