import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

export interface EncryptedApiKeyData {
  uuid: string;
  encryptedKey: string;
  nonce: string;
  tag: string;
  ephemeralPubKey: string;
  status: 'active' | 'inactive' | 'revoked';
  createdAt: string;
  scope: string;
  keyId?: string; // For targeting specific executors
}

export interface ExecutorKeysResult {
  keys: EncryptedApiKeyData[];
  totalCount: number;
}

@Injectable()
export class ExecutorKeyService {
  private readonly logger = new Logger(ExecutorKeyService.name);
  private etcdClient!: Etcd3;

  constructor(private configService: ConfigService) {
    this.initializeEtcdClient();
  }

  private initializeEtcdClient(): void {
    const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
    const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);

    this.etcdClient = new Etcd3({
      hosts: [`${etcdHost}:${etcdPort}`],
    });

    this.logger.log(`ExecutorKeyService connected to etcd at ${etcdHost}:${etcdPort}`);
  }

  /**
   * Get encrypted API keys for a specific scope (provider:region)
   * According to design document section 4.2
   * Path: /api-keys/{provider}/{region}/{uuid}
   */
  async getEncryptedKeysForScope(
    provider: string,
    region: string,
    executorId: string,
  ): Promise<ExecutorKeysResult> {
    try {
      this.logger.debug(
        `Getting encrypted keys for scope ${provider}:${region}, executor: ${executorId}`,
      );

      // Build etcd path prefix according to design document
      const keyPrefix = `/api-keys/${provider}/${region}/`;
      
      // Get all keys for this provider/region
      const kvs = await this.etcdClient.getAll().prefix(keyPrefix);
      
      const encryptedKeys: EncryptedApiKeyData[] = [];

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string);
          
          // Extract UUID from path (last part)
          const pathParts = fullPath.split('/');
          const uuid = pathParts[pathParts.length - 1];

          // Only include active keys
          if (keyData.status !== 'active') {
            this.logger.debug(`Skipping inactive key ${uuid} with status: ${keyData.status}`);
            continue;
          }

          // Check if key is targeted for this specific executor (if keyId is specified)
          if (keyData.keyId && keyData.keyId !== executorId) {
            this.logger.debug(`Skipping key ${uuid} - targeted for different executor: ${keyData.keyId}`);
            continue;
          }

          // Validate required encryption fields
          if (!keyData.encryptedKey || !keyData.nonce || !keyData.tag || !keyData.ephemeralPubKey) {
            this.logger.warn(`Skipping key ${uuid} - missing required encryption fields`);
            continue;
          }

          const encryptedKey: EncryptedApiKeyData = {
            uuid,
            encryptedKey: keyData.encryptedKey,
            nonce: keyData.nonce,
            tag: keyData.tag,
            ephemeralPubKey: keyData.ephemeralPubKey,
            status: keyData.status,
            createdAt: keyData.createdAt || new Date().toISOString(),
            scope: `${provider}:${region}`,
            keyId: keyData.keyId,
          };

          encryptedKeys.push(encryptedKey);
          this.logger.debug(`Added encrypted key ${uuid} for scope ${provider}:${region}`);
        } catch (parseError) {
          this.logger.warn(
            `Failed to parse encrypted key data for ${fullPath}: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
          );
        }
      }

      this.logger.log(
        `Found ${encryptedKeys.length} encrypted keys for scope ${provider}:${region}, executor: ${executorId}`,
      );

      return {
        keys: encryptedKeys,
        totalCount: encryptedKeys.length,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get encrypted keys for scope ${provider}:${region}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get all available scopes (provider:region combinations)
   * Useful for debugging and monitoring
   */
  async getAvailableScopes(): Promise<string[]> {
    try {
      const prefix = '/api-keys/';
      const kvs = await this.etcdClient.getAll().prefix(prefix);
      
      const scopes = new Set<string>();

      for (const [fullPath] of Object.entries(kvs)) {
        // Parse path: /api-keys/{provider}/{region}/{uuid}
        const pathParts = fullPath.split('/');
        if (pathParts.length >= 4) {
          const provider = pathParts[2];
          const region = pathParts[3];
          scopes.add(`${provider}:${region}`);
        }
      }

      const scopeList = Array.from(scopes).sort();
      this.logger.debug(`Available scopes: ${scopeList.join(', ')}`);
      
      return scopeList;
    } catch (error) {
      this.logger.error(
        `Failed to get available scopes: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get statistics about encrypted keys
   * Useful for monitoring and debugging
   */
  async getKeyStatistics(): Promise<{
    totalKeys: number;
    keysByScope: Record<string, number>;
    keysByStatus: Record<string, number>;
  }> {
    try {
      const prefix = '/api-keys/';
      const kvs = await this.etcdClient.getAll().prefix(prefix);
      
      const keysByScope: Record<string, number> = {};
      const keysByStatus: Record<string, number> = {};
      let totalKeys = 0;

      for (const [fullPath, jsonValue] of Object.entries(kvs)) {
        try {
          const keyData = JSON.parse(jsonValue as string);
          const pathParts = fullPath.split('/');
          
          if (pathParts.length >= 4) {
            const provider = pathParts[2];
            const region = pathParts[3];
            const scope = `${provider}:${region}`;
            
            keysByScope[scope] = (keysByScope[scope] || 0) + 1;
            keysByStatus[keyData.status || 'unknown'] = (keysByStatus[keyData.status || 'unknown'] || 0) + 1;
            totalKeys++;
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse key data for statistics: ${fullPath}`);
        }
      }

      return {
        totalKeys,
        keysByScope,
        keysByStatus,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get key statistics: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Validate scope format
   */
  static validateScope(scope: string): { valid: boolean; provider?: string; region?: string; error?: string } {
    if (!scope) {
      return { valid: false, error: 'Scope is required' };
    }

    const parts = scope.split(':');
    if (parts.length !== 2) {
      return { valid: false, error: 'Scope must be in format provider:region' };
    }

    const [provider, region] = parts;
    
    if (!provider || !region) {
      return { valid: false, error: 'Both provider and region must be non-empty' };
    }

    // Validate provider and region format (alphanumeric and hyphens only)
    const validFormat = /^[a-zA-Z0-9-]+$/;
    if (!validFormat.test(provider) || !validFormat.test(region)) {
      return { valid: false, error: 'Provider and region must contain only alphanumeric characters and hyphens' };
    }

    return { valid: true, provider, region };
  }
}
