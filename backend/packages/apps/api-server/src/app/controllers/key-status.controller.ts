import {
  Controller,
  Get,
  Put,
  Query,
  Param,
  Body,
  Logger,
  HttpException,
  HttpStatus,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@saito/auth';
import { KeyStatusService } from '../services/key-status.service';
import { AuthenticatedRequest } from '../types/request.types';

export interface UpdateKeyStatusRequest {
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked';
  note?: string;
  reason?: string;
}

export interface KeyStatusResponse {
  keyId: string;
  key: string;
  status: 'active' | 'rate_limited' | 'exhausted' | 'revoked';
  lastUsedAt: number;
  lastError?: string;
  note?: string;
  provider: string;
}

@ApiTags('key-status')
@Controller('key-status')
export class KeyStatusController {
  private readonly logger = new Logger(KeyStatusController.name);

  constructor(private readonly keyStatusService: KeyStatusService) {}

  /**
   * Get all API key statuses for a provider
   * Implements Gateway's ability to view all key statuses
   */
  @Get(':provider')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all API key statuses for a provider',
    description: 'Retrieves all API key statuses from etcd for the specified provider',
  })
  @ApiParam({
    name: 'provider',
    description: 'Provider name (openai, claude, anthropic, etc.)',
    example: 'openai',
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    enum: ['active', 'rate_limited', 'exhausted', 'revoked'],
  })
  @ApiResponse({
    status: 200,
    description: 'API key statuses retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            keys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  keyId: { type: 'string' },
                  key: { type: 'string' },
                  status: { type: 'string', enum: ['active', 'rate_limited', 'exhausted', 'revoked'] },
                  lastUsedAt: { type: 'number' },
                  lastError: { type: 'string' },
                  note: { type: 'string' },
                  provider: { type: 'string' },
                },
              },
            },
            statistics: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                active: { type: 'number' },
                rateLimited: { type: 'number' },
                exhausted: { type: 'number' },
                revoked: { type: 'number' },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getAllKeyStatuses(
    @Param('provider') provider: string,
    @Query('status') statusFilter?: string,
    @Request() req?: AuthenticatedRequest,
  ) {
    try {
      this.logger.log(`Getting all key statuses for provider: ${provider}`);

      const result = await this.keyStatusService.getAllKeyStatuses(provider, statusFilter);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get key statuses for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException('Failed to get key statuses', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get specific API key status
   */
  @Get(':provider/:keyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get specific API key status',
    description: 'Retrieves status for a specific API key',
  })
  @ApiParam({ name: 'provider', description: 'Provider name' })
  @ApiParam({ name: 'keyId', description: 'API key ID' })
  @ApiResponse({
    status: 200,
    description: 'API key status retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'API key not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getKeyStatus(
    @Param('provider') provider: string,
    @Param('keyId') keyId: string,
    @Request() req?: AuthenticatedRequest,
  ) {
    try {
      this.logger.log(`Getting status for key ${keyId} in provider ${provider}`);

      const status = await this.keyStatusService.getKeyStatus(provider, keyId);

      if (!status) {
        throw new HttpException('API key not found', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: status,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to get status for key ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException('Failed to get key status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update API key status (for Provider/Admin use)
   */
  @Put(':provider/:keyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update API key status',
    description: 'Updates the status of a specific API key (Provider/Admin only)',
  })
  @ApiParam({ name: 'provider', description: 'Provider name' })
  @ApiParam({ name: 'keyId', description: 'API key ID' })
  @ApiResponse({
    status: 200,
    description: 'API key status updated successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'API key not found' })
  async updateKeyStatus(
    @Param('provider') provider: string,
    @Param('keyId') keyId: string,
    @Body() updateRequest: UpdateKeyStatusRequest,
    @Request() req?: AuthenticatedRequest,
  ) {
    try {
      this.logger.log(`Updating status for key ${keyId} to ${updateRequest.status}`);

      const success = await this.keyStatusService.updateKeyStatus(
        provider,
        keyId,
        updateRequest.status,
        updateRequest.reason,
        updateRequest.note,
      );

      if (!success) {
        throw new HttpException('Failed to update key status', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      return {
        success: true,
        message: 'API key status updated successfully',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update status for key ${keyId}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException('Failed to update key status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get key status statistics for monitoring
   */
  @Get(':provider/stats/summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get key status statistics',
    description: 'Retrieves aggregated statistics for all keys of a provider',
  })
  @ApiParam({ name: 'provider', description: 'Provider name' })
  @ApiResponse({
    status: 200,
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            active: { type: 'number' },
            rateLimited: { type: 'number' },
            exhausted: { type: 'number' },
            revoked: { type: 'number' },
            lastUpdated: { type: 'number' },
          },
        },
      },
    },
  })
  async getKeyStatistics(
    @Param('provider') provider: string,
    @Request() req?: AuthenticatedRequest,
  ) {
    try {
      this.logger.log(`Getting statistics for provider: ${provider}`);

      const stats = await this.keyStatusService.getKeyStatistics(provider);

      return {
        success: true,
        data: {
          ...stats,
          lastUpdated: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get statistics for provider ${provider}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new HttpException('Failed to get key statistics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Watch key status changes (Server-Sent Events)
   */
  @Get(':provider/watch')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Watch key status changes',
    description: 'Stream real-time key status changes using Server-Sent Events',
  })
  @ApiParam({ name: 'provider', description: 'Provider name' })
  async watchKeyStatusChanges(
    @Param('provider') provider: string,
    @Request() req?: AuthenticatedRequest,
  ) {
    // This would implement Server-Sent Events for real-time updates
    // For now, return a message indicating the feature
    return {
      success: true,
      message: 'Real-time key status watching is available via WebSocket or SSE',
      endpoint: `/key-status/${provider}/watch`,
    };
  }
}
