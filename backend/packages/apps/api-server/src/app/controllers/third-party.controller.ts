import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from "@saito/auth";
import { ApiKeyService, ThirdPartyKeyService } from "@saito/apikey";
import {
  CreateThirdPartyKeyRequest
} from '@saito/models';
import { AuthenticatedRequest, ApiError } from '../types/request.types';
import { EncryptedApiKeyService } from '../services/encrypted-api-key.service';

@ApiTags('third-party')
@Controller('/third-party')
export class ThirdPartyController {
  private readonly logger = new Logger(ThirdPartyController.name);

  constructor(
    private readonly thirdPartyKeyService: ThirdPartyKeyService,
    private readonly encryptedApiKeyService: EncryptedApiKeyService
  ) {}

  // 辅助函数：处理API错误
  private handleApiError(error: unknown, defaultMessage: string): never {
    const apiError = error as ApiError;

    if (error instanceof HttpException) {
      throw error;
    }

    throw new HttpException(
      apiError.message || defaultMessage,
      apiError.status || HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  // =============================================
  // Third-Party Key Management Endpoints
  // =============================================

  @Post()
  // @UseGuards(JwtAuthGuard)  // Temporarily disabled for testing
  // @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create a third-party API key',
    description: 'Creates a new third-party API key with KMS encryption'
  })
  @ApiResponse({
    status: 201,
    description: 'Third-party API key created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            keyType: { type: 'string' },
            status: { type: 'string' },
            createdAt: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createThirdPartyKey(
    @Body() request: CreateThirdPartyKeyRequest,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const result = await this.thirdPartyKeyService.createThirdPartyKey(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      const apiError = error as ApiError;
      throw new HttpException(
        apiError.message || 'Failed to create third-party API key',
        apiError.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }



  @Get('third-party-keys')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get third-party keys for user',
    description: 'Retrieves all third-party keys associated with the authenticated user'
  })
  @ApiResponse({
    status: 200,
    description: 'Third-party keys retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  provider: { type: 'string' },
                  status: { type: 'string' },
                  createdAt: { type: 'string' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getThirdPartyKeys(
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;
      const result = await this.thirdPartyKeyService.getThirdPartyKeys(userId, { status: 'active' });

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get third-party keys');
    }
  }

  @Delete(':keyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remove a third-party API key',
    description: 'Removes a third-party API key and cleans up relationships'
  })
  @ApiParam({ name: 'keyId', description: 'Third-party API key ID' })
  @ApiResponse({
    status: 200,
    description: 'Third-party API key removed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Third-party key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async removeThirdPartyKey(
    @Param('keyId') keyId: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      await this.thirdPartyKeyService.removeThirdPartyKey(keyId, userId);

      return {
        success: true
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to remove third-party key');
    }
  }

  // =============================================
  // Encrypted API Key Management Endpoints
  // =============================================

  @Post('encrypted')
  @UseGuards(JwtAuthGuard)  // Temporarily disabled for testing
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create an encrypted API key',
    description: 'Creates a new encrypted API key using X25519 + ChaCha20-Poly1305 encryption. The API key is encrypted by the frontend and stored in etcd for Executor decryption.'
  })
  @ApiResponse({
    status: 201,
    description: 'Encrypted API key created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            uuid: { type: 'string', description: 'Unique identifier for the encrypted key' },
            provider: { type: 'string', description: 'Provider name' },
            region: { type: 'string', description: 'Region name' },
            keyId: { type: 'string', description: 'Target executor key ID' },
            status: { type: 'string', description: 'Key status' },
            createdAt: { type: 'string', description: 'Creation timestamp' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - invalid encryption data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status:404, description: 'Target executor not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createEncryptedApiKey(
    @Body() request: {
      provider: string;
      region: string;
      keyId: string;                  // 目标 Executor 的 keyId
      encryptedKey: string;          // base64(ciphertext) 加密后的Provider API KEY
      nonce: string;                 // base64(nonce) 随机数，ChaCha20-Poly1305要求解密用
      tag: string;                   // base64(tag) ChaCha20-Poly1305要求解密用
      ephemeralPubKey: string;       // base64(X25519 公钥)
    },
    @Request() req: AuthenticatedRequest
  ) {
    try {
      // For testing with disabled auth, use a default userId
      const userId = req.user?.userId || 'test-user-001';

      // Validate request data
      if (!request.provider || !request.region || !request.keyId ||
          !request.encryptedKey || !request.nonce || request.tag === undefined || !request.ephemeralPubKey) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Missing required encryption fields',
              code: 'MISSING_ENCRYPTION_FIELDS',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(`Creating encrypted API key for provider: ${request.provider}, region: ${request.region}, keyId: ${request.keyId}`);
      const result = await this.encryptedApiKeyService.createEncryptedApiKey(request, userId);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      if (error instanceof HttpException) {
        throw error;
      }

      const apiError = error as ApiError;
      this.logger.error(`Failed to create encrypted API key: ${apiError.message}`);

      throw new HttpException(
        {
          success: false,
          error: {
            message: apiError.message || 'Failed to create encrypted API key',
            code: 'ENCRYPTION_ERROR',
          },
        },
        apiError.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('encrypted')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get encrypted API keys for user',
    description: 'Retrieves all encrypted API keys associated with the authenticated user'
  })
  @ApiQuery({ name: 'provider', required: false, description: 'Filter by provider' })
  @ApiQuery({ name: 'region', required: false, description: 'Filter by region' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'pageSize', required: false, description: 'Page size', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Encrypted API keys retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  provider: { type: 'string' },
                  status: { type: 'string' },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' },
                  totalRequests: { type: 'number' },
                  totalTokens: { type: 'number' },
                  totalCost: { type: 'number' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getEncryptedApiKeys(
    @Request() req: AuthenticatedRequest,
    @Query('provider') provider?: string,
    @Query('region') region?: string,
    @Query('status') status?: string,
    @Query('page') page?: string,
    @Query('pageSize') pageSize?: string,
  ) {
    try {
      const userId = req.user.userId;

      const options = {
        provider,
        region,
        status,
        page: page ? parseInt(page, 10) : undefined,
        pageSize: pageSize ? parseInt(pageSize, 10) : undefined,
      };

      const result = await this.encryptedApiKeyService.listEncryptedApiKeysFromDatabase(userId, options);

      return {
        success: true,
        data: result
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to get encrypted API keys');
    }
  }

  @Put('encrypted/:uuid/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update encrypted API key status',
    description: 'Updates the status of an encrypted API key'
  })
  @ApiParam({ name: 'uuid', description: 'Encrypted API key UUID' })
  @ApiResponse({
    status: 200,
    description: 'Encrypted API key status updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Encrypted API key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateEncryptedApiKeyStatus(
    @Param('uuid') uuid: string,
    @Body() request: {
      provider: string;
      region: string;
      status: 'active' | 'revoked' | 'waiting-to-verify';
    },
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const success = await this.encryptedApiKeyService.updateEncryptedApiKeyStatus(
        request.provider,
        request.region,
        uuid,
        request.status,
        userId
      );

      if (!success) {
        throw new HttpException(
          'Encrypted API key not found or access denied',
          HttpStatus.NOT_FOUND
        );
      }

      return {
        success: true
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to update encrypted API key status');
    }
  }

  @Delete('encrypted/:uuid')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete an encrypted API key',
    description: 'Deletes an encrypted API key from both database and etcd'
  })
  @ApiParam({ name: 'uuid', description: 'Encrypted API key UUID' })
  @ApiResponse({
    status: 200,
    description: 'Encrypted API key deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Encrypted API key not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async deleteEncryptedApiKey(
    @Param('uuid') uuid: string,
    @Body() request: {
      provider: string;
      region: string;
    },
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;

      const success = await this.encryptedApiKeyService.deleteEncryptedApiKey(
        request.provider,
        request.region,
        uuid,
        userId
      );

      if (!success) {
        throw new HttpException(
          'Encrypted API key not found or access denied',
          HttpStatus.NOT_FOUND
        );
      }

      return {
        success: true
      };
    } catch (error: unknown) {
      return this.handleApiError(error, 'Failed to delete encrypted API key');
    }
  }
}
