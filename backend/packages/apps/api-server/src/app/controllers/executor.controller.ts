import {
  Controller,
  Get,
  Query,
  Logger,
  HttpException,
  HttpStatus,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { ExecutorKeyService } from '../services/executor-key.service';
import { ExecutorAuthGuard } from '../guards/executor-auth.guard';
import { AuthenticatedExecutorRequest } from '../types/request.types';

@ApiTags('executor')
@Controller('executor')
export class ExecutorController {
  private readonly logger = new Logger(ExecutorController.name);

  constructor(private readonly executorKeyService: ExecutorKeyService) {}

  /**
   * Request API key allocation for executor by scope
   * Endpoint: /executor/key?scope=claude:asia
   * According to design document section 4.2
   * Allocates and assigns specific keys to the requesting executor
   */
  @Get('key')
  @UseGuards(ExecutorAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Request API key allocation for executor',
    description: 'Allocates and assigns encrypted API keys for the specified scope to this executor',
  })
  @ApiQuery({
    name: 'scope',
    description: 'Scope in format provider:region (e.g., claude:asia, openai:us, deepseek:global)',
    example: 'claude:asia',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Encrypted API keys retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            keys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  uuid: { type: 'string', description: 'Unique identifier for the key' },
                  encryptedKey: { type: 'string', description: 'Base64 encoded encrypted API key' },
                  nonce: { type: 'string', description: 'Base64 encoded nonce' },
                  tag: { type: 'string', description: 'Base64 encoded authentication tag' },
                  ephemeralPubKey: { type: 'string', description: 'Base64 encoded ephemeral public key' },
                  status: { type: 'string', enum: ['active', 'inactive', 'revoked'] },
                  createdAt: { type: 'string', format: 'date-time' },
                  scope: { type: 'string', description: 'Scope this key is bound to' },
                },
              },
            },
            totalCount: { type: 'number', description: 'Total number of available keys' },
            scope: { type: 'string', description: 'Requested scope' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid scope format',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid executor credentials',
  })
  @ApiResponse({
    status: 404,
    description: 'No keys found for the specified scope',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getExecutorKeys(
    @Query('scope') scope: string,
    @Request() req: AuthenticatedExecutorRequest,
  ) {
    try {
      // Validate scope parameter
      if (!scope) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Scope parameter is required',
              code: 'MISSING_SCOPE',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Validate scope format: provider:region
      const scopePattern = /^[a-zA-Z0-9-]+:[a-zA-Z0-9-]+$/;
      if (!scopePattern.test(scope)) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: 'Invalid scope format. Expected format: provider:region (e.g., claude:asia)',
              code: 'INVALID_SCOPE_FORMAT',
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const [provider, region] = scope.split(':');
      const executorId = req.executor.id;
      const executorRegion = req.executor.region;

      this.logger.log(
        `Executor ${executorId} requesting keys for scope: ${scope} (executor region: ${executorRegion})`,
      );

      // Get encrypted API keys for the specified scope
      const result = await this.executorKeyService.getEncryptedKeysForScope(
        provider,
        region,
        executorId,
      );

      if (!result || result.keys.length === 0) {
        throw new HttpException(
          {
            success: false,
            error: {
              message: `No active API keys found for scope: ${scope}`,
              code: 'NO_KEYS_FOUND',
            },
          },
          HttpStatus.NOT_FOUND,
        );
      }

      this.logger.log(
        `Successfully retrieved ${result.keys.length} encrypted keys for executor ${executorId}, scope: ${scope}`,
      );

      return {
        success: true,
        data: {
          keys: result.keys,
          totalCount: result.totalCount,
          scope: scope,
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to get executor keys for scope ${scope}:`,
        error instanceof Error ? error.message : String(error),
      );

      throw new HttpException(
        {
          success: false,
          error: {
            message: 'Internal server error while retrieving API keys',
            code: 'INTERNAL_ERROR',
          },
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Health check endpoint for executors
   */
  @Get('health')
  @ApiOperation({
    summary: 'Executor health check',
    description: 'Health check endpoint for executor services',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            status: { type: 'string', example: 'healthy' },
            timestamp: { type: 'string', format: 'date-time' },
            version: { type: 'string' },
          },
        },
      },
    },
  })
  async healthCheck() {
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    };
  }
}
