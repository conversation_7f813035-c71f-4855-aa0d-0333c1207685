import { Modu<PERSON> } from '@nestjs/common';
import { APP_PIPE, DiscoveryService } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { ZodValidationPipe } from 'nestjs-zod';
import { <PERSON><PERSON><PERSON>eyController } from './controllers/apikey.controller';
import { AuthController } from './controllers/auth.controller';
import { EarningsController } from './controllers/earnings.controller';
import { ExecutorController } from './controllers/executor.controller';
import { IndexController } from './controllers/index.controller';
import { KeyStatusController } from './controllers/key-status.controller';
import { NodeController } from './controllers/node.controller';
import { OllamaChatController } from './controllers/ollama.controller';
import { OpenAIController } from './controllers/openai.controller';
import { PublicKeysController } from './controllers/public-keys.controller';
import { ThirdPartyController } from './controllers/third-party.controller';

import { ScheduleModule } from '@nestjs/schedule';
import { ApiKeyModule } from '@saito/apikey';
import { AuthModule } from '@saito/auth';
import { EarningsModule } from '@saito/earnings';
import { NodeModule } from '@saito/node';
import { NodeMetricsModule } from '@saito/node-metrics';
import { OllamaModule } from '@saito/ollama';
import { OpenAIModule } from '@saito/openai';
import { TaskManagerModule } from '@saito/task-manager';
import { DidModule } from "@saito/did";
import { MessageHandlerRegistry, TunnelModule } from '@saito/tunnel';
import { Libp2pController } from './controllers/tunnel-libp2p.controller';
import { ExecutorKeyService } from './services/executor-key.service';
import { KeyStatusService } from './services/key-status.service';
import { PublicKeyService } from './services/public-key.service';
import { EncryptedApiKeyService } from './services/encrypted-api-key.service';
import { ExecutorAuthGuard } from './guards/executor-auth.guard';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ScheduleModule.forRoot(),
    AuthModule,
    NodeModule,
    NodeMetricsModule,
    EarningsModule,
    ApiKeyModule,
    TunnelModule,
    TaskManagerModule,
    OpenAIModule,
    OllamaModule,
    DidModule,
  ],
  controllers: [
    IndexController,
    AuthController,
    NodeController,
    EarningsController,
    ApiKeyController,
    ThirdPartyController,
    ExecutorController,
    KeyStatusController,
    PublicKeysController,
    OllamaChatController,
    OpenAIController,
    Libp2pController,
  ],
  providers: [
    DiscoveryService,
    ExecutorKeyService,
    KeyStatusService,
    PublicKeyService,
    EncryptedApiKeyService,
    ExecutorAuthGuard,
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
  ],
})
export class AppModule {}
