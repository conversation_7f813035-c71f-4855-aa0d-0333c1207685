import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { EncryptedApiKeyService } from '../src/app/services/encrypted-api-key.service';
import { PublicKeyService } from '../src/app/services/public-key.service';
import { ApiKeyRepository } from '@saito/apikey';
import { PersistentModule } from '@saito/persistent';

describe('EncryptedApiKeyService', () => {
  let service: EncryptedApiKeyService;
  let apiKeyRepository: ApiKeyRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
        PersistentModule,
      ],
      providers: [
        EncryptedApiKeyService,
        {
          provide: PublicKeyService,
          useValue: {
            isExecutorAvailable: jest.fn().mockResolvedValue(true),
          },
        },
        ApiKeyRepository,
      ],
    }).compile();

    service = module.get<EncryptedApiKeyService>(EncryptedApiKeyService);
    apiKeyRepository = module.get<ApiKeyRepository>(ApiKeyRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(apiKeyRepository).toBeDefined();
  });

  describe('createEncryptedApiKey', () => {
    it('should create encrypted API key in both database and etcd', async () => {
      const mockRequest = {
        provider: 'openai',
        region: 'us-east-1',
        keyId: 'test-executor-key-id',
        encryptedKey: Buffer.from('encrypted-key-data').toString('base64'),
        nonce: Buffer.from('nonce-data').toString('base64'),
        tag: Buffer.from('tag-data').toString('base64'),
        ephemeralPubKey: Buffer.from('ephemeral-pub-key-data').toString('base64'),
      };

      const userId = 'test-user-001';

      // Mock the database creation
      const mockDbRecord = {
        id: 'test-uuid',
        userId,
        name: 'Encrypted openai key',
        status: 'waiting-to-verify',
        keyType: 'third_party',
        provider: 'openai',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      jest.spyOn(apiKeyRepository, 'createEncryptedApiKey').mockResolvedValue(mockDbRecord as any);

      const result = await service.createEncryptedApiKey(mockRequest, userId);

      expect(result).toBeDefined();
      expect(result.provider).toBe('openai');
      expect(result.region).toBe('us-east-1');
      expect(result.keyId).toBe('test-executor-key-id');
      expect(result.status).toBe('waiting-to-verify');

      // Verify database was called
      expect(apiKeyRepository.createEncryptedApiKey).toHaveBeenCalledWith({
        userId,
        uuid: expect.any(String),
        provider: 'openai',
        region: 'us-east-1',
        name: 'Encrypted openai key',
        description: 'Encrypted API key for openai in us-east-1',
      });
    });

    it('should throw error for invalid base64 data', async () => {
      const mockRequest = {
        provider: 'openai',
        region: 'us-east-1',
        keyId: 'test-executor-key-id',
        encryptedKey: 'invalid-base64!@#',
        nonce: 'base64-nonce',
        tag: 'base64-tag',
        ephemeralPubKey: 'base64-ephemeral-pub-key',
      };

      const userId = 'test-user-001';

      await expect(service.createEncryptedApiKey(mockRequest, userId)).rejects.toThrow();
    });
  });

  describe('listEncryptedApiKeysFromDatabase', () => {
    it('should list encrypted API keys from database', async () => {
      const userId = 'test-user-001';
      const mockDbResult = {
        items: [
          {
            id: 'test-uuid-1',
            name: 'Encrypted openai key',
            provider: 'openai',
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            totalRequests: 10,
            totalTokens: 1000,
            totalCost: 0.05,
          },
        ],
        total: 1,
        page: 1,
        pageSize: 10,
      };

      jest.spyOn(apiKeyRepository, 'getEncryptedApiKeys').mockResolvedValue(mockDbResult as any);

      const result = await service.listEncryptedApiKeysFromDatabase(userId);

      expect(result).toBeDefined();
      expect(result.items).toHaveLength(1);
      expect(result.items[0].provider).toBe('openai');
      expect(result.total).toBe(1);

      // Verify database was called
      expect(apiKeyRepository.getEncryptedApiKeys).toHaveBeenCalledWith(userId, undefined);
    });
  });
});
