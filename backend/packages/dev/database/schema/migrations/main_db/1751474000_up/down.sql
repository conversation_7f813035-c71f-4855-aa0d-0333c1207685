-- Migration rollback: Restore original KMS constraints
-- Date: 2025-07-23
-- Description: Rollback the flexible KMS constraint changes

-- 1. Drop the new indexes
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_encrypted_keys;
DROP INDEX IF EXISTS saito_gateway.idx_api_keys_key_prefix;

-- 2. Drop the new flexible constraint
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_kms_data_flexible;

-- 3. Restore the original strict KMS constraint
ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_kms_data 
    CHECK (
        (key_type = 'platform' AND kms_key_id IS NULL AND encrypted_key_data IS NULL) OR 
        (key_type = 'third_party' AND kms_key_id IS NOT NULL AND encrypted_key_data IS NOT NULL)
    );

-- 4. Restore the original provider constraint
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_provider_updated;
ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_provider 
    CHECK (
        (key_type = 'platform' AND provider IS NULL) OR 
        (key_type = 'third_party' AND provider IN ('openai', 'anthropic', 'google', 'cohere'))
    );
