-- Migration: Remove KMS constraints for encrypted API keys
-- Date: 2025-07-23
-- Description: 
-- 1. Remove the strict KMS constraint that requires kms_key_id and encrypted_key_data for all third_party keys
-- 2. Allow encrypted API keys to store encrypted data without KMS fields
-- 3. Update constraints to be more flexible for different types of third-party keys

-- 1. Drop the existing KMS constraint
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_kms_data;

-- 2. Add a more flexible constraint that allows:
--    - Platform keys: no KMS fields required
--    - Third-party keys with KMS: both kms_key_id and encrypted_key_data required
--    - Third-party encrypted keys: only encrypted_key_data required (for etcd-stored encrypted keys)
ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_kms_data_flexible 
    CHECK (
        -- Platform keys: no KMS fields
        (key_type = 'platform' AND kms_key_id IS NULL AND encrypted_key_data IS NULL) OR 
        -- Third-party keys with KMS encryption
        (key_type = 'third_party' AND kms_key_id IS NOT NULL AND encrypted_key_data IS NOT NULL) OR
        -- Third-party encrypted keys (stored in etcd): only encrypted_key_data required
        (key_type = 'third_party' AND kms_key_id IS NULL AND encrypted_key_data IS NOT NULL AND key_prefix = 'enc-')
    );

-- 3. Add a comment to document the constraint logic
COMMENT ON CONSTRAINT chk_api_keys_kms_data_flexible ON saito_gateway.api_keys IS 
'Flexible KMS constraint: Platform keys have no KMS fields, third-party keys can use KMS or be encrypted (etcd-stored with enc- prefix)';

-- 4. Update the provider constraint to include more providers if needed
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS chk_api_keys_provider;
ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_provider_updated 
    CHECK (
        (key_type = 'platform' AND provider IS NULL) OR 
        (key_type = 'third_party' AND provider IN ('openai', 'anthropic', 'google', 'cohere', 'azure', 'aws'))
    );

-- 5. Add index for encrypted keys specifically
CREATE INDEX IF NOT EXISTS idx_api_keys_encrypted_keys ON saito_gateway.api_keys(user_id, provider, status) 
    WHERE key_type = 'third_party' AND key_prefix = 'enc-';

-- 6. Add index for key prefix to optimize encrypted key queries
CREATE INDEX IF NOT EXISTS idx_api_keys_key_prefix ON saito_gateway.api_keys(key_prefix) 
    WHERE key_prefix IS NOT NULL;
