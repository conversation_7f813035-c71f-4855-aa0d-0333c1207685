-- =============================================
-- 新迁移：客户端加密 + KMS 存储架构
-- 版本：1751473000
-- 描述：实现双重加密的第三方 API 密钥管理系统
-- =============================================

BEGIN;

-- 1. 删除旧的 api_categories 表和相关约束（如果存在）
ALTER TABLE saito_gateway.api_keys DROP CONSTRAINT IF EXISTS api_keys_category_id_fkey;
ALTER TABLE saito_gateway.api_usage DROP CONSTRAINT IF EXISTS api_usage_category_id_fkey;
DROP TABLE IF EXISTS saito_gateway.api_categories CASCADE;

-- 2. 删除 api_keys 表中的 category_id 列
ALTER TABLE saito_gateway.api_keys DROP COLUMN IF EXISTS category_id;

-- 3. 删除 api_usage 表中的 category_id 列
ALTER TABLE saito_gateway.api_usage DROP COLUMN IF EXISTS category_id;

-- 4. 添加新的列到 api_keys 表
DO $$
BEGIN
    -- 添加 key_type 列
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'key_type') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN key_type VARCHAR(50) NOT NULL DEFAULT 'platform';
        RAISE NOTICE '✅ 添加了 key_type 列';
    ELSE
        RAISE NOTICE '⚠️  key_type 列已存在';
    END IF;

    -- 添加 provider 列（用于第三方密钥）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'provider') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN provider VARCHAR(50) NULL;
        RAISE NOTICE '✅ 添加了 provider 列';
    ELSE
        RAISE NOTICE '⚠️  provider 列已存在';
    END IF;

    -- 添加 kms_key_id 列
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'kms_key_id') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN kms_key_id VARCHAR(255) NULL;
        RAISE NOTICE '✅ 添加了 kms_key_id 列';
    ELSE
        RAISE NOTICE '⚠️  kms_key_id 列已存在';
    END IF;

    -- 添加 encrypted_key_data 列
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'encrypted_key_data') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN encrypted_key_data TEXT NULL;
        RAISE NOTICE '✅ 添加了 encrypted_key_data 列';
    ELSE
        RAISE NOTICE '⚠️  encrypted_key_data 列已存在';
    END IF;

    -- 添加统计相关列
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'total_requests') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN total_requests INTEGER DEFAULT 0;
        RAISE NOTICE '✅ 添加了 total_requests 列';
    ELSE
        RAISE NOTICE '⚠️  total_requests 列已存在';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'total_tokens') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN total_tokens INTEGER DEFAULT 0;
        RAISE NOTICE '✅ 添加了 total_tokens 列';
    ELSE
        RAISE NOTICE '⚠️  total_tokens 列已存在';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'saito_gateway'
                   AND table_name = 'api_keys'
                   AND column_name = 'total_cost') THEN
        ALTER TABLE saito_gateway.api_keys ADD COLUMN total_cost DECIMAL(10, 6) DEFAULT 0;
        RAISE NOTICE '✅ 添加了 total_cost 列';
    ELSE
        RAISE NOTICE '⚠️  total_cost 列已存在';
    END IF;
END $$;

-- 5. 添加约束
ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_key_type 
    CHECK (key_type IN ('platform', 'third_party'));

ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_provider 
    CHECK (
        (key_type = 'platform' AND provider IS NULL) OR 
        (key_type = 'third_party' AND provider IN ('openai', 'anthropic', 'google', 'cohere'))
    );

ALTER TABLE saito_gateway.api_keys ADD CONSTRAINT chk_api_keys_kms_data 
    CHECK (
        (key_type = 'platform' AND kms_key_id IS NULL AND encrypted_key_data IS NULL) OR 
        (key_type = 'third_party' AND kms_key_id IS NOT NULL AND encrypted_key_data IS NOT NULL)
    );

-- 6. 创建索引
CREATE INDEX IF NOT EXISTS idx_api_keys_key_type ON saito_gateway.api_keys(key_type);
CREATE INDEX IF NOT EXISTS idx_api_keys_provider ON saito_gateway.api_keys(provider) WHERE provider IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_api_keys_kms_key_id ON saito_gateway.api_keys(kms_key_id) WHERE kms_key_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_api_keys_type_status ON saito_gateway.api_keys(key_type, status);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_type ON saito_gateway.api_keys(user_id, key_type);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_provider ON saito_gateway.api_keys(user_id, provider) WHERE provider IS NOT NULL;

-- 7. 添加列注释
COMMENT ON COLUMN saito_gateway.api_keys.key_type IS 
'Type of API key: platform (for authentication/billing) or third_party (for external API access)';

COMMENT ON COLUMN saito_gateway.api_keys.provider IS 
'Provider for third-party keys: openai, anthropic, google, cohere (null for platform keys)';

COMMENT ON COLUMN saito_gateway.api_keys.kms_key_id IS 
'KMS key identifier for encrypted third-party keys (null for platform keys)';

COMMENT ON COLUMN saito_gateway.api_keys.encrypted_key_data IS 
'KMS encrypted data containing client-side encrypted API key information (null for platform keys)';

COMMENT ON COLUMN saito_gateway.api_keys.total_requests IS 
'Total number of API requests made with this key';

COMMENT ON COLUMN saito_gateway.api_keys.total_tokens IS 
'Total number of tokens consumed by this key';

COMMENT ON COLUMN saito_gateway.api_keys.total_cost IS 
'Total cost incurred by this key in USD';

-- 8. 更新现有数据（如果有的话）
UPDATE saito_gateway.api_keys 
SET key_type = 'platform' 
WHERE key_type IS NULL OR key_type = '';

-- 9. 创建视图用于方便查询
CREATE OR REPLACE VIEW saito_gateway.v_api_keys_summary AS
SELECT 
    id,
    user_id,
    name,
    key_type,
    provider,
    status,
    key_mask,
    total_requests,
    total_tokens,
    total_cost,
    created_at,
    updated_at,
    last_used,
    CASE 
        WHEN key_type = 'platform' THEN 'Platform Key'
        WHEN key_type = 'third_party' THEN CONCAT('Third-party (', UPPER(provider), ')')
        ELSE 'Unknown'
    END AS key_description,
    CASE 
        WHEN kms_key_id IS NOT NULL THEN 'KMS Encrypted'
        ELSE 'Standard'
    END AS encryption_status
FROM saito_gateway.api_keys
WHERE deleted_at IS NULL;

COMMENT ON VIEW saito_gateway.v_api_keys_summary IS 
'Summary view of API keys with human-readable descriptions';

-- 10. 创建函数用于统计更新
CREATE OR REPLACE FUNCTION saito_gateway.update_api_key_stats(
    p_api_key_id UUID,
    p_requests_delta INTEGER DEFAULT 1,
    p_tokens_delta INTEGER DEFAULT 0,
    p_cost_delta DECIMAL(10, 6) DEFAULT 0
) RETURNS VOID AS $$
BEGIN
    UPDATE saito_gateway.api_keys 
    SET 
        total_requests = total_requests + p_requests_delta,
        total_tokens = total_tokens + p_tokens_delta,
        total_cost = total_cost + p_cost_delta,
        last_used = NOW(),
        updated_at = NOW()
    WHERE id = p_api_key_id;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION saito_gateway.update_api_key_stats IS 
'Update API key usage statistics atomically';

COMMIT;

-- 11. 验证迁移结果
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'saito_gateway' 
  AND table_name = 'api_keys'
  AND column_name IN ('key_type', 'provider', 'kms_key_id', 'encrypted_key_data', 'total_requests', 'total_tokens', 'total_cost')
ORDER BY column_name;

SELECT '🎉 客户端加密 + KMS 存储架构迁移完成！' AS status;
