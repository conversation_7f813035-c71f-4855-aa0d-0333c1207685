import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ApiKeyRepository } from './apikey.repository';
import { EtcdService } from './etcd.service';
import {
  CreateThirdPartyKeyRequest,
  UpdateThirdPartyKeyRequest,
  ApiKey
} from '@saito/models';
import * as crypto from 'crypto'
/**
 * Third-Party Key Management Service
 * Handles creation, management, and storage of third-party API keys
 * Stores metadata in database and indexes in etcd
 */
@Injectable()
export class ThirdPartyKeyService {
  private readonly logger = new Logger(ThirdPartyKeyService.name);

  constructor(
    private readonly apiKeyRepository: ApiKeyRepository,
    private readonly etcdService: EtcdService
  ) {}

  /**
   * Create a new third-party API key (simplified version without encryption)
   */
  async createThirdPartyKey(
    request: CreateThirdPartyKeyRequest,
    userId: string
  ): Promise<ApiKey> {
    try {
      this.logger.debug(`Creating third-party key for user ${userId}`);

      // Generate etcd key for indexing
      const etcdKey = `/third-party-keys/${request.provider}/${userId}`;

      // Generate a hash for the actual API key
      const keyHash = crypto.createHash('sha256').update(request.apiKey).digest('hex');

      // Create the API key record in database (simplified)
      const apiKey = await this.apiKeyRepository.createThirdPartyKey({
        userId,
        name: request.name,
        description: request.description,
        provider: request.provider,
        keyHash
      });

      // Store key information in etcd for Executor access (simplified)
      const dynamicEtcdKey = `${etcdKey}/${apiKey.id}`;
      const etcdData = {
        keyId: apiKey.id,
        userId: apiKey.userId,
        provider: request.provider,
        status: apiKey.status,
        keyType: 'third_party' as const,
        createdAt: new Date(apiKey.createdAt).toISOString(),
        encryptionType: 'none'
      };

      await this.storeKeyInEtcd(dynamicEtcdKey, etcdData);

      this.logger.log(`Created third-party key ${apiKey.id} for user ${userId}`);
      return apiKey;
    } catch (error) {
      this.logger.error(`Failed to create third-party key: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party key information (metadata only)
   */
  async getThirdPartyKeyInfo(keyId: string): Promise<{
    keyType: string;
  }> {
    try {
      this.logger.debug(`Retrieving third-party key info ${keyId}`);

      // Get key information from database
      const keyInfo = await this.apiKeyRepository.getThirdPartyKeyInfo(keyId);
      if (!keyInfo) {
        throw new NotFoundException(`Third-party key ${keyId} not found`);
      }

      if (keyInfo.keyType !== 'third_party') {
        throw new Error(`Key ${keyId} is not a third-party key`);
      }

      this.logger.debug(`Successfully retrieved third-party key info ${keyId}`);
      return { keyType: keyInfo.keyType };
    } catch (error) {
      this.logger.error(`Failed to get third-party key info ${keyId}: ${error}`);
      throw error;
    }
  }





  /**
   * Store key information in etcd
   */
  private async storeKeyInEtcd(keyId: string, keyInfo: {
    keyId: string;
    userId: string;
    provider: string;
    status: string;
    keyType: string;
    createdAt: string;
    encryptionType: string;
  }): Promise<void> {
    try {
      await this.etcdService.set(keyId, JSON.stringify(keyInfo));
    } catch (error) {
      this.logger.warn(`Failed to store key ${keyId} in etcd: ${error}`);
      // Don't throw - etcd is for caching, not critical
    }
  }



  /**
   * Get third-party keys for a user
   */
  async getThirdPartyKeys(userId: string, options?: {
    provider?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      return await this.apiKeyRepository.getThirdPartyKeys(userId, options);
    } catch (error) {
      this.logger.error(`Failed to get third-party keys for user ${userId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party key by ID
   */
  async getThirdPartyKeyById(keyId: string, userId: string): Promise<ApiKey> {
    try {
      const key = await this.apiKeyRepository.getApiKeyById(keyId);

      if (!key || key.userId !== userId || key.keyType !== 'third_party') {
        throw new NotFoundException('Third-party key not found or access denied');
      }

      return key;
    } catch (error) {
      this.logger.error(`Failed to get third-party key ${keyId}: ${error}`);
      throw error;
    }
  }

  /**
   * Update third-party key
   */
  async updateThirdPartyKey(keyId: string, request: UpdateThirdPartyKeyRequest, userId: string): Promise<ApiKey> {
    try {
      // Verify ownership
      const existingKey = await this.getThirdPartyKeyById(keyId, userId);

      // Update the key
      const updatedKey = await this.apiKeyRepository.updateThirdPartyKey(keyId, request);

      // Update etcd if status changed
      if (request.status && request.status !== existingKey.status && existingKey.provider) {
        // Reconstruct etcd key path
        const etcdKeyPath = `/third-party-keys/${existingKey.provider}/${existingKey.userId}/${existingKey.id}`;
        await this.updateEtcdKeyStatus(etcdKeyPath, request.status);
      }

      this.logger.log(`Updated third-party key ${keyId} for user ${userId}`);
      return updatedKey;
    } catch (error) {
      this.logger.error(`Failed to update third-party key ${keyId}: ${error}`);
      throw error;
    }
  }

  /**
   * Update key status in etcd
   */
  private async updateEtcdKeyStatus(etcdKey: string, status: string): Promise<void> {
    try {
      const existingData = await this.etcdService.get(etcdKey);
      if (existingData) {
        const keyInfo = JSON.parse(existingData);
        keyInfo.status = status;
        await this.etcdService.set(etcdKey, JSON.stringify(keyInfo));
      }
    } catch (error) {
      this.logger.warn(`Failed to update etcd key status: ${error}`);
    }
  }



  /**
   * Remove a third-party key
   */
  async removeThirdPartyKey(keyId: string, userId: string): Promise<void> {
    try {
      this.logger.debug(`Removing third-party key ${keyId}`);

      // Verify ownership
      const key = await this.apiKeyRepository.getApiKeyById(keyId);
      if (!key || key.userId !== userId) {
        throw new NotFoundException('Third-party key not found or access denied');
      }

      if (key.keyType !== 'third_party') {
        throw new Error('Key is not a third-party key');
      }

      // Remove from database (soft delete)
      await this.apiKeyRepository.deleteApiKey(keyId, userId);

      // Remove from etcd
      if (key.provider && key.keyType === 'third_party') {
        // Reconstruct etcd key path
        const etcdKeyPath = `/third-party-keys/${key.provider}/${key.userId}/${key.id}`;
        await this.etcdService.delete(etcdKeyPath);
      }

      this.logger.log(`Removed third-party key ${keyId}`);
    } catch (error) {
      this.logger.error(`Failed to remove third-party key ${keyId}: ${error}`);
      throw error;
    }
  }


}
