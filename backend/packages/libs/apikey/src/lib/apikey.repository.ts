import { Injectable, Logger } from '@nestjs/common';
import { PersistentService } from '@saito/persistent';
import { SQL as sql } from '@saito/common';
import {
  ApiKey,
  ApiKeyDetail,
  ApiKeyStats,
  ChannelUsage,
  ApiKeyValidationResult,
  ApiUsageLogParams,
  ApiUsageLogUpdateParams,
  UpdateThirdPartyKeyRequest,
  UsageSummary,
  OverallUsage,
  ApiKeyRow,
  DailyRequestRow,
  ModelUsageRow,
  ChannelUsageRow
} from '@saito/models';
import * as CryptoJS from 'crypto-js';
import { z } from 'zod';

@Injectable()
export class ApiKeyRepository {
  private readonly logger = new Logger(ApiKeyRepository.name);

  constructor(private readonly persistentService: PersistentService) {}

  /**
   * Generate a new API key
   * @returns The generated API key and its prefix
   */
  async generateApiKey(): Promise<{ key: string; prefix: string }> {
    try {
      // Use a standard prefix for all API keys
      const keyPrefix = 'sk-';

      // Generate a random key based on the pattern
      // Generate a 32-character random string
      const randomPart = Array.from(
        { length: 32 },
        () => Math.floor(Math.random() * 36).toString(36)
      ).join('');

      // Add salt (sight ai) and hash with SHA-256
      const salt = 'sight ai';
      const saltedRandomPart = CryptoJS.SHA256(salt + randomPart).toString().substring(0, 32);

      // Combine prefix and salted random part
      const key = `${keyPrefix}${saltedRandomPart}`;

      return { key, prefix: keyPrefix };
    } catch (error) {
      this.logger.error(`Error generating API key: ${error}`);
      throw error;
    }
  }

  /**
   * Create a new API key (platform key)
   * @param userId The user ID
   * @param name The key name
   * @param key The generated API key
   * @param keyPrefix The key prefix
   * @returns The created API key
   */
  async createApiKey(
    userId: string,
    name: string,
    key: string,
    keyPrefix: string
  ): Promise<ApiKey> {
    try {
      // Hash the API key for storage with salt
      const salt = 'sight ai';
      const keyHash = CryptoJS.SHA256(salt + key).toString();

      // Create a masked version of the key for display
      const keyMask = `${keyPrefix}...${key.slice(-4)}`;

      // Insert the new API key (platform type by default)
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.api_keys (
          user_id,
          key_hash,
          key_prefix,
          key_mask,
          name,
          status,
          key_type
        )
        VALUES (
          ${userId},
          ${keyHash},
          ${keyPrefix},
          ${keyMask},
          ${name},
          'active',
          'platform'
        )
        RETURNING
          id,
          user_id as "userId",
          key_hash as "keyHash",
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          name,
          status,
          key_type as "keyType",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
      `);

      if (result.rows.length === 0) {
        throw new Error('Failed to create API key');
      }

      return result.rows[0] as ApiKey;
    } catch (error) {
      this.logger.error(`Error creating API key: ${error}`);
      throw error;
    }
  }

  /**
   * Create a new third-party API key
   */
  async createThirdPartyKey(params: {
    userId: string;
    name: string;
    description?: string;
    provider: string;
    keyHash: string;
  }): Promise<ApiKey> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.api_keys (
          user_id,
          name,
          description,
          status,
          key_type,
          provider,
          key_prefix,
          key_mask,
          key_hash
        )
        VALUES (
          ${params.userId},
          ${params.name},
          ${params.description || null},
          'active',
          'third_party',
          ${params.provider},
          'tp-',
          'tp-***simple***',
          ${params.keyHash}
        )
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider as "providerKeyId",
          kms_key_id as "kmsKeyId",
          encrypted_key_data as "encryptedKeyData",
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
      `);

      if (result.rows.length === 0) {
        throw new Error('Failed to create third-party API key');
      }

      return result.rows[0] as ApiKey;
    } catch (error) {
      this.logger.error(`Error creating third-party API key: ${error}`);
      throw error;
    }
  }

  /**
   * Create a new encrypted API key record in database
   */
  async createEncryptedApiKey(params: {
    userId: string;
    uuid: string;
    provider: string;
    region: string;
    name?: string;
    description?: string;
  }): Promise<ApiKey> {
    try {
      const name = params.name || `Encrypted ${params.provider} key`;
      const description = params.description || `Encrypted API key for ${params.provider} in ${params.region}`;

      const result = await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.api_keys (
          id,
          user_id,
          name,
          description,
          status,
          key_type,
          provider,
          key_prefix,
          key_mask,
          key_hash
        )
        VALUES (
          ${params.uuid},
          ${params.userId},
          ${name},
          ${description},
          'waiting-to-verify',
          'third_party',
          ${params.provider},
          'enc-',
          'enc-***encrypted***',
          'encrypted-key-placeholder'
        )
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider as "providerKeyId",
          kms_key_id as "kmsKeyId",
          encrypted_key_data as "encryptedKeyData",
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
      `);

      if (result.rows.length === 0) {
        throw new Error('Failed to create encrypted API key');
      }

      return result.rows[0] as ApiKey;
    } catch (error) {
      this.logger.error(`Error creating encrypted API key: ${error}`);
      throw error;
    }
  }

  /**
   * Get encrypted API keys for a user
   */
  async getEncryptedApiKeys(userId: string, options?: {
    provider?: string;
    region?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const page = options?.page || 1;
      const pageSize = options?.pageSize || 10;
      const offset = (page - 1) * pageSize;

      // Build WHERE clause for encrypted keys
      let whereClause = sql.fragment`WHERE user_id = ${userId} AND key_type = 'third_party' AND key_prefix = 'enc-' AND deleted_at IS NULL`;

      if (options?.provider) {
        whereClause = sql.fragment`${whereClause} AND provider = ${options.provider}`;
      }

      if (options?.status) {
        whereClause = sql.fragment`${whereClause} AND status = ${options.status}`;
      }

      // Get total count
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as count
        FROM saito_gateway.api_keys
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].count);

      // Get paginated results
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
        FROM saito_gateway.api_keys
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      return {
        items: result.rows as ApiKey[],
        total,
        page,
        pageSize
      };
    } catch (error) {
      this.logger.error(`Error getting encrypted API keys for user ${userId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get third-party keys for a user
   */
  async getThirdPartyKeys(userId: string, options?: {
    provider?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: ApiKey[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const page = options?.page || 1;
      const pageSize = options?.pageSize || 10;
      const offset = (page - 1) * pageSize;

      // Build WHERE clause
      let whereClause = sql.fragment`WHERE user_id = ${userId} AND key_type = 'third_party' AND deleted_at IS NULL`;

      if (options?.provider) {
        whereClause = sql.fragment`${whereClause} AND provider = ${options.provider}`;
      }

      if (options?.status) {
        whereClause = sql.fragment`${whereClause} AND status = ${options.status}`;
      }

      // Get total count
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as total
        FROM saito_gateway.api_keys
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          last_used as "lastUsed"
        FROM saito_gateway.api_keys
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      return {
        items: result.rows as ApiKey[],
        total,
        page,
        pageSize
      };
    } catch (error) {
      this.logger.error(`Error getting third-party keys: ${error}`);
      throw error;
    }
  }

  /**
   * Update third-party key
   */
  async updateThirdPartyKey(keyId: string, updates: UpdateThirdPartyKeyRequest | { etcdKey: string }): Promise<ApiKey> {
    try {
      let query = sql.unsafe`UPDATE saito_gateway.api_keys SET updated_at = NOW()`;

      if ('name' in updates && updates.name !== undefined) {
        query = sql.unsafe`${query}, name = ${updates.name}`;
      }

      if ('description' in updates && updates.description !== undefined) {
        query = sql.unsafe`${query}, description = ${updates.description}`;
      }

      if ('status' in updates && updates.status !== undefined) {
        query = sql.unsafe`${query}, status = ${updates.status}`;
      }

      if ('etcdKey' in updates && updates.etcdKey !== undefined) {
        query = sql.unsafe`${query}, etcd_key = ${updates.etcdKey}`;
      }

      query = sql.unsafe`${query} WHERE id = ${keyId} AND deleted_at IS NULL
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          last_used as "lastUsed"`;

      const result = await this.persistentService.pgPool.query(query);

      if (result.rows.length === 0) {
        throw new Error('Third-party key not found or already deleted');
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error(`Error updating third-party key: ${error}`);
      throw error;
    }
  }

  /**
   * Update an API key
   * @param keyId The key ID
   * @param userId The user ID
   * @param name The new key name (optional)
   * @param status The new key status (optional)
   */
  async updateApiKey(
    keyId: string,
    userId: string,
    name?: string,
    status?: string
  ): Promise<void> {
    try {
      // 使用SQL模板字符串构建查询
      let updateQuery = sql.fragment`UPDATE saito_gateway.api_keys SET updated_at = NOW()`;

      if (name !== undefined) {
        updateQuery = sql.fragment`${updateQuery}, name = ${name}`;
      }

      if (status !== undefined) {
        updateQuery = sql.fragment`${updateQuery}, status = ${status}`;
      }

      // 添加WHERE条件
      updateQuery = sql.fragment`${updateQuery} WHERE id = ${keyId} AND user_id = ${userId} AND deleted_at IS NULL`;

      // 执行更新
      await this.persistentService.pgPool.query(sql.unsafe`${updateQuery}`);
    } catch (error) {
      this.logger.error(`Error updating API key: ${error}`);
      throw error;
    }
  }

  /**
   * Delete an API key (soft delete)
   * @param keyId The key ID
   * @param userId The user ID
   */
  async deleteApiKey(keyId: string, userId: string): Promise<void> {
    try {
      // Soft delete the API key
      const updateQuery = sql.fragment`
        UPDATE saito_gateway.api_keys
        SET deleted_at = NOW(), status = 'revoked', updated_at = NOW()
        WHERE id = ${keyId} AND user_id = ${userId} AND deleted_at IS NULL
      `;

      await this.persistentService.pgPool.query(sql.unsafe`${updateQuery}`);
    } catch (error) {
      this.logger.error(`Error deleting API key: ${error}`);
      throw error;
    }
  }

  /**
   * Get API keys for a user
   * @param userId The user ID
   * @param page The page number
   * @param pageSize The page size
   * @param status Filter by status (optional)
   * @param search Search term (optional)
   * @returns The API keys and total count
   */
  async getApiKeys(
    userId: string,
    page: number,
    pageSize: number,
    status?: string,
    search?: string
  ): Promise<{ data: ApiKey[]; total: number }> {
    try {
      // Build the WHERE clause based on filters
      let whereClause = sql.fragment`WHERE k.user_id = ${userId} AND k.deleted_at IS NULL`;

      if (status) {
        whereClause = sql.fragment`${whereClause} AND k.status = ${status}`;
      }

      if (search) {
        whereClause = sql.fragment`${whereClause} AND k.name ILIKE ${`%${search}%`}`;
      }

      // Get total count
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as total
        FROM saito_gateway.api_keys k
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].total, 10);

      // Get API keys with pagination
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          k.id,
          k.user_id as "userId",
          k.key_hash as "keyHash",
          k.key_prefix as "keyPrefix",
          k.key_mask as "keyMask",
          k.name,
          k.status,
          k.key_type as "keyType",
          k.kms_key_id as "kmsKeyId",
          k.provider as "providerKeyId",
          k.encrypted_key_data as "encryptedKeyData",
          k.total_requests as "totalRequests",
          k.total_tokens as "totalTokens",
          k.total_cost as "totalCost",
          k.created_at as "createdAt",
          k.updated_at as "updatedAt",
          k.deleted_at as "deletedAt",
          k.description,
          k.expiration_date as "expirationDate",
          (
            SELECT MAX(au.timestamp)
            FROM saito_gateway.api_usage au
            WHERE au.api_key_id = k.id
          ) as "lastUsed",
          u.username as "createdBy"
        FROM saito_gateway.api_keys k
        LEFT JOIN saito_gateway.users u ON k.user_id = u.id
        ${whereClause}
        ORDER BY k.created_at DESC
        LIMIT ${pageSize}
        OFFSET ${(page - 1) * pageSize}
      `);

      // Transform the data to match the expected format
      const data: ApiKey[] = result.rows.map((row: ApiKeyRow) => ({
        id: row.id,
        userId: row.userId,
        keyHash: row.keyHash,
        keyPrefix: row.keyPrefix,
        keyMask: row.keyMask,
        name: row.name,
        status: row.status,
        keyType: row.keyType || 'platform',
        kmsKeyId: row.kmsKeyId,
        providerKeyId: row.providerKeyId,
        encryptedKeyData: row.encryptedKeyData,
        totalRequests: row.totalRequests || 0,
        totalTokens: row.totalTokens || 0,
        totalCost: row.totalCost || 0,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        deletedAt: row.deletedAt,
        expirationDate: row.expirationDate,
        lastUsed: row.lastUsed,
        description: row.description
      }));

      return { data, total };
    } catch (error) {
      this.logger.error(`Error getting API keys: ${error}`);
      // Return empty data in case of error
      return { data: [], total: 0 };
    }
  }



  /**
   * Get API key detail
   * @param keyId The key ID
   * @param userId The user ID
   * @returns The API key detail
   */
  async getApiKeyDetail(keyId: string, userId: string): Promise<ApiKeyDetail | null> {
    try {

      // 首先检查API密钥是否存在，不考虑用户ID
      const keyExistsResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.api_keys WHERE id = ${keyId}
      `);

      if (keyExistsResult.rows.length === 0) {

        return null;
      }

      // 检查API密钥是否属于该用户
      const userKeyResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.api_keys WHERE id = ${keyId} AND user_id = ${userId}
      `);

      if (userKeyResult.rows.length === 0) {

        return null;
      }

      // 检查API密钥是否已删除
      const deletedKeyResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.api_keys WHERE id = ${keyId} AND deleted_at IS NOT NULL
      `);

      if (deletedKeyResult.rows.length > 0) {

        return null;
      }

      // 获取API密钥详情
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          k.id,
          k.name,
          k.key_mask as "keyMask",
          c.name as "category",
          c.provider,
          k.status,
          k.created_at as "createdAt",
          k.updated_at as "updatedAt"
        FROM saito_gateway.api_keys k
        WHERE k.id = ${keyId} AND k.user_id = ${userId} AND k.deleted_at IS NULL
      `);

      if (result.rows.length === 0) {

        return null;
      }



      // 获取请求统计数据
      const requestsStatsResult = await this.persistentService.pgPool.query(sql.unsafe`
        WITH usage_data AS (
          SELECT
            COUNT(*) as total_requests,
            SUM(CASE WHEN date >= CURRENT_DATE - INTERVAL '30 days' THEN 1 ELSE 0 END) as recent_requests,
            SUM(CASE WHEN date >= CURRENT_DATE - INTERVAL '60 days' AND date < CURRENT_DATE - INTERVAL '30 days' THEN 1 ELSE 0 END) as previous_requests
          FROM saito_gateway.api_usage
          WHERE api_key_id = ${keyId} AND user_id = ${userId}
        )
        SELECT
          COALESCE(total_requests, 0) as total_requests,
          CASE
            WHEN COALESCE(previous_requests, 0) = 0 THEN 0
            ELSE ((COALESCE(recent_requests, 0) - COALESCE(previous_requests, 0)) / COALESCE(previous_requests, 1)) * 100
          END as requests_change
        FROM usage_data
      `);

      // 获取令牌使用统计数据
      const tokensStatsResult = await this.persistentService.pgPool.query(sql.unsafe`
        WITH token_data AS (
          SELECT
            SUM(total_tokens) as total_tokens,
            SUM(CASE WHEN date >= CURRENT_DATE - INTERVAL '30 days' THEN total_tokens ELSE 0 END) as recent_tokens,
            SUM(CASE WHEN date >= CURRENT_DATE - INTERVAL '60 days' AND date < CURRENT_DATE - INTERVAL '30 days' THEN total_tokens ELSE 0 END) as previous_tokens
          FROM saito_gateway.token_usage
          WHERE api_key_id = ${keyId} AND user_id = ${userId}
        )
        SELECT
          COALESCE(total_tokens, 0) as total_tokens,
          CASE
            WHEN COALESCE(previous_tokens, 0) = 0 THEN 0
            ELSE ((COALESCE(recent_tokens, 0) - COALESCE(previous_tokens, 0)) / COALESCE(previous_tokens, 1)) * 100
          END as tokens_change
        FROM token_data
      `);

      // 获取成本统计数据
      const costStatsResult = await this.persistentService.pgPool.query(sql.unsafe`
        WITH cost_data AS (
          SELECT
            SUM(cost) as total_cost,
            SUM(CASE WHEN date >= CURRENT_DATE - INTERVAL '30 days' THEN cost ELSE 0 END) as recent_cost,
            SUM(CASE WHEN date >= CURRENT_DATE - INTERVAL '60 days' AND date < CURRENT_DATE - INTERVAL '30 days' THEN cost ELSE 0 END) as previous_cost
          FROM saito_gateway.token_usage
          WHERE api_key_id = ${keyId} AND user_id = ${userId}
        )
        SELECT
          COALESCE(total_cost, 0) as total_cost,
          CASE
            WHEN COALESCE(previous_cost, 0) = 0 THEN 0
            ELSE ((COALESCE(recent_cost, 0) - COALESCE(previous_cost, 0)) / COALESCE(previous_cost, 1)) * 100
          END as cost_change
        FROM cost_data
      `);

      // 获取最近的计费记录
      const billingResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          billing_date as "billingDate",
          amount
        FROM saito_gateway.billing_records
        WHERE api_key_id = ${keyId} AND user_id = ${userId}
        ORDER BY billing_date DESC
        LIMIT 1
      `);

      // 计算统计数据
      const stats: ApiKeyStats = {
        requests: requestsStatsResult.rows.length > 0 ? parseInt(requestsStatsResult.rows[0].total_requests, 10) : 0,
        requestsChange: requestsStatsResult.rows.length > 0 ? parseFloat(requestsStatsResult.rows[0].requests_change) : 0,
        tokens: tokensStatsResult.rows.length > 0 ? parseInt(tokensStatsResult.rows[0].total_tokens, 10) : 0,
        tokensChange: tokensStatsResult.rows.length > 0 ? parseFloat(tokensStatsResult.rows[0].tokens_change) : 0,
        cost: costStatsResult.rows.length > 0 ? parseFloat(costStatsResult.rows[0].total_cost) : 0,
        lastCharge: billingResult.rows.length > 0 ? billingResult.rows[0].billingDate : new Date().toISOString().split('T')[0]
      };

      // 转换数据为预期格式
      const data: ApiKeyDetail = {
        id: result.rows[0].id,
        name: result.rows[0].name || '',
        key: result.rows[0].keyMask,
        provider: result.rows[0].provider,
        status: result.rows[0].status,
        description: result.rows[0].description || null,
        expirationDate: result.rows[0].expirationDate || null,
        lastUsed: result.rows[0].lastUsed || null,
        createdAt: result.rows[0].createdAt,
        updatedAt: result.rows[0].updatedAt,
        stats
      };

      return data;
    } catch (error) {
      this.logger.error(`Error getting API key detail: ${error}`);
      return null;
    }
  }

  /**
   * Get API key usage
   * @param keyId The key ID
   * @param userId The user ID
   * @param timeRange Time range in days (optional)
   * @returns The API key usage data
   */
  async getApiKeyUsage(
    keyId: string,
    userId: string,
    timeRange?: string
  ): Promise<{
    dailyRequests: { date: string; count: number }[];
    totalRequests: number;
    totalTokens: number;
    avgResponseTime: number;
    costThisMonth: number;
    usageByModel: { model: string; requests: number; tokens: number; cost: number }[];
  }> {
    try {

      // 首先检查API密钥是否存在
      const keyExistsResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.api_keys WHERE id = ${keyId}
      `);

      if (keyExistsResult.rows.length === 0) {

        throw new Error(`API key with ID ${keyId} not found`);
      }

      // 计算时间范围过滤器
      let timeFilter = sql.fragment``;
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilter = sql.fragment`AND date >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
      }

      // 获取每日请求数
      const dailyQuerySql = `
        SELECT
          date::text,
          COUNT(*) as count
        FROM saito_gateway.api_usage
        WHERE api_key_id = '${keyId}' ${timeFilter ? timeFilter.sql : ''}
        GROUP BY date
        ORDER BY date ASC
      `;

      const dailyResult = await this.persistentService.pgPool.query(sql.unsafe([dailyQuerySql]));


      // 获取总请求数和其他指标
      const totalsQuerySql = `
        SELECT
          COUNT(*) as total_requests,
          AVG(COALESCE(response_time, 0)) as avg_response_time
        FROM saito_gateway.api_usage
        WHERE api_key_id = '${keyId}' ${timeFilter ? timeFilter.sql : ''}
      `;

      const totalsResult = await this.persistentService.pgPool.query(sql.unsafe([totalsQuerySql]));


      // 获取令牌使用情况
      const tokensQuerySql = `
        SELECT
          SUM(total_tokens) as total_tokens,
          SUM(CASE WHEN date >= DATE_TRUNC('month', CURRENT_DATE) THEN cost ELSE 0 END) as cost_this_month
        FROM saito_gateway.token_usage
        WHERE api_key_id = '${keyId}' ${timeFilter ? timeFilter.sql : ''}
      `;

      const tokensResult = await this.persistentService.pgPool.query(sql.unsafe([tokensQuerySql]));


      // 获取按模型分组的使用情况
      const modelQuerySql = `
        SELECT
          model,
          COUNT(*) as requests,
          SUM(total_tokens) as tokens,
          SUM(cost) as cost
        FROM saito_gateway.token_usage
        WHERE api_key_id = '${keyId}' ${timeFilter ? timeFilter.sql : ''}
        GROUP BY model
        ORDER BY requests DESC
      `;

      const modelResult = await this.persistentService.pgPool.query(sql.unsafe([modelQuerySql]));


      // 转换数据为预期格式
      const dailyRequests = dailyResult.rows.map((row: DailyRequestRow) => ({
        date: row.date,
        count: parseInt(row.count, 10)
      }));

      const totalRequests = totalsResult.rows.length > 0 ? parseInt(totalsResult.rows[0].total_requests, 10) : 0;
      const avgResponseTime = totalsResult.rows.length > 0 ? parseFloat(totalsResult.rows[0].avg_response_time) : 0;
      const totalTokens = tokensResult.rows.length > 0 ? parseInt(tokensResult.rows[0].total_tokens, 10) || 0 : 0;
      const costThisMonth = tokensResult.rows.length > 0 ? parseFloat(tokensResult.rows[0].cost_this_month) || 0 : 0;

      const usageByModel = modelResult.rows.map((row: ModelUsageRow) => ({
        model: row.model,
        requests: parseInt(row.requests, 10) || 0,
        tokens: parseInt(row.tokens, 10) || 0,
        cost: parseFloat(row.cost) || 0
      }));

      // 如果没有数据，生成一些默认的每日数据
      if (dailyRequests.length === 0 && timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dailyRequests.push({
              date: date.toISOString().split('T')[0],
              count: 0
            });
          }
          // 按日期排序
          dailyRequests.sort((a, b) => a.date.localeCompare(b.date));
        }
      }

      const result = {
        dailyRequests,
        totalRequests,
        totalTokens,
        avgResponseTime,
        costThisMonth,
        usageByModel
      };


      return result;
    } catch (error) {
      this.logger.error(`[API-KEY-USAGE] Error getting API key usage: ${error instanceof Error ? error.message : String(error)}`);
      if (error instanceof Error && error.stack) {
        this.logger.error(`[API-KEY-USAGE] Stack trace: ${error.stack}`);
      }

      // 返回默认值
      return {
        dailyRequests: [],
        totalRequests: 0,
        totalTokens: 0,
        avgResponseTime: 0,
        costThisMonth: 0,
        usageByModel: []
      };
    }
  }

  /**
   * Get channel usage
   * @param userId The user ID
   * @param timeRange Time range in days (optional)
   * @returns The channel usage data
   */
  async getChannelUsage(
    userId: string,
    timeRange?: string
  ): Promise<ChannelUsage[]> {
    try {
      // 计算时间范围过滤器
      let timeFilterClause = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterClause = `AND tu.date >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
      }

      // 获取渠道使用情况
      const channelUsageSql = `
        WITH api_usage_data AS (
          SELECT
            c.provider,
            tu.model,
            COUNT(DISTINCT au.id) as requests,
            SUM(tu.total_tokens) as tokens,
            SUM(tu.cost) as cost,
            AVG(COALESCE(au.response_time, 0)) as avg_response_time
          FROM saito_gateway.token_usage tu
          JOIN saito_gateway.api_keys k ON tu.api_key_id = k.id
          LEFT JOIN saito_gateway.api_usage au ON tu.api_key_id = au.api_key_id AND tu.date = au.date
          WHERE tu.user_id = '${userId}' ${timeFilterClause}
          GROUP BY c.provider, tu.model
        )
        SELECT
          provider,
          model,
          requests,
          tokens,
          cost,
          avg_response_time
        FROM api_usage_data
        ORDER BY requests DESC
      `;
      const result = await this.persistentService.pgPool.query(sql.unsafe([channelUsageSql]));

      // 转换数据为预期格式
      const data: ChannelUsage[] = result.rows.map((row: ChannelUsageRow) => ({
        provider: row.provider,
        model: row.model,
        requests: parseInt(row.requests, 10),
        tokens: parseInt(row.tokens, 10),
        cost: parseFloat(row.cost),
        avgResponseTime: parseFloat(row.avg_response_time)
      }));

      // 如果没有数据，添加一些默认数据
      // if (data.length === 0) {
      //   // 获取所有API类别
      //   const categoriesResult = await this.persistentService.pgPool.query(sql.unsafe`
      //     SELECT DISTINCT provider FROM saito_gateway.api_categories WHERE is_active = TRUE
      //   `);

      //   // 为每个提供商添加默认数据
      //   for (const row of categoriesResult.rows) {
      //     data.push({
      //       provider: row.provider,
      //       model: 'Default',
      //       requests: 0,
      //       tokens: 0,
      //       cost: 0,
      //       avgResponseTime: 0
      //     });
      //   }
      // }

      return data;
    } catch (error) {
      this.logger.error(`Error getting channel usage: ${error}`);
      // 返回空数据
      return [];
    }
  }

  /**
   * Get usage summary data
   * @param userId The user ID
   * @param timeRange Time range in days (optional)
   * @returns The usage summary data
   */
  async getUsageSummary(
    userId: string,
    timeRange?: string
  ): Promise<UsageSummary> {
    try {
      // 计算时间范围过滤器
      let timeFilterSql = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterSql = `AND date >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
      }

      // 获取总请求数
      const requestsQuerySql = `
        SELECT COUNT(DISTINCT id) as total_requests
        FROM saito_gateway.api_usage
        WHERE user_id = '${userId}' ${timeFilterSql}
      `;
      const requestsResult = await this.persistentService.pgPool.query(sql.unsafe([requestsQuerySql]));

      // 获取总Token数和本月费用
      const tokensQuerySql = `
        SELECT
          SUM(total_tokens) as total_tokens,
          SUM(CASE WHEN date >= DATE_TRUNC('month', CURRENT_DATE) THEN cost ELSE 0 END) as cost_this_month
        FROM saito_gateway.token_usage
        WHERE user_id = '${userId}' ${timeFilterSql}
      `;
      const tokensResult = await this.persistentService.pgPool.query(sql.unsafe([tokensQuerySql]));

      // 获取平均响应时间
      const responseTimeQuerySql = `
        SELECT AVG(response_time) as avg_response_time
        FROM saito_gateway.api_usage
        WHERE user_id = '${userId}' ${timeFilterSql} AND response_time IS NOT NULL
      `;
      const responseTimeResult = await this.persistentService.pgPool.query(sql.unsafe([responseTimeQuerySql]));

      // 提取数据
      const totalRequests = parseInt(requestsResult.rows[0]?.total_requests || '0', 10);
      const totalTokens = parseInt(tokensResult.rows[0]?.total_tokens || '0', 10);
      const costThisMonth = parseFloat(tokensResult.rows[0]?.cost_this_month || '0');
      const avgResponseTime = Math.round(parseFloat(responseTimeResult.rows[0]?.avg_response_time || '0'));

      return {
        totalRequests,
        totalTokens,
        avgResponseTime,
        costThisMonth
      };
    } catch (error) {
      this.logger.error(`Error getting usage summary: ${error}`);
      // 返回默认数据
      return {
        totalRequests: 0,
        totalTokens: 0,
        avgResponseTime: 0,
        costThisMonth: 0
      };
    }
  }

  /**
   * Get overall usage data for reports
   * @param userId The user ID
   * @param timeRange Time range in days (optional)
   * @returns The overall usage data
   */
  async getOverallUsage(
    userId: string,
    timeRange?: string
  ): Promise<OverallUsage> {
    try {
      // 计算时间范围过滤器
      let timeFilterSql = '';
      if (timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          timeFilterSql = `AND tu.date >= CURRENT_DATE - INTERVAL '${days} days'`;
        }
      }

      // 获取每日使用数据
      const dailyQuerySql = `
        SELECT
          tu.date::text as date,
          COUNT(DISTINCT au.id)::text as requests,
          SUM(tu.total_tokens)::text as tokens,
          SUM(tu.cost)::text as cost
        FROM saito_gateway.token_usage tu
        JOIN saito_gateway.api_keys k ON tu.api_key_id = k.id
        LEFT JOIN saito_gateway.api_usage au ON tu.api_key_id = au.api_key_id AND tu.date = au.date
        WHERE tu.user_id = '${userId}' ${timeFilterSql}
        GROUP BY tu.date
        ORDER BY tu.date
      `;
      const dailyResult = await this.persistentService.pgPool.query(sql.unsafe([dailyQuerySql]));

      // 获取月度使用数据
      const monthlyQuerySql = `
        SELECT
          EXTRACT(YEAR FROM tu.date)::text as year,
          EXTRACT(MONTH FROM tu.date)::text as month,
          COUNT(DISTINCT au.id)::text as requests,
          SUM(tu.total_tokens)::text as tokens,
          SUM(tu.cost)::text as cost
        FROM saito_gateway.token_usage tu
        JOIN saito_gateway.api_keys k ON tu.api_key_id = k.id
        LEFT JOIN saito_gateway.api_usage au ON tu.api_key_id = au.api_key_id AND tu.date = au.date
        WHERE tu.user_id = '${userId}'
        GROUP BY EXTRACT(YEAR FROM tu.date), EXTRACT(MONTH FROM tu.date)
        ORDER BY year, month
      `;
      const monthlyResult = await this.persistentService.pgPool.query(sql.unsafe([monthlyQuerySql]));

      // 转换每日数据为预期格式
      const dailyData = dailyResult.rows.map((row) => ({
        date: row.date,
        requests: parseInt(row.requests || '0', 10),
        tokens: parseInt(row.tokens || '0', 10),
        cost: parseFloat(row.cost || '0')
      }));

      // 转换月度数据为预期格式
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthlyData = monthlyResult.rows.map((row) => ({
        month: months[parseInt(row.month, 10) - 1],
        year: parseInt(row.year, 10),
        requests: parseInt(row.requests || '0', 10),
        tokens: parseInt(row.tokens || '0', 10),
        cost: parseFloat(row.cost || '0')
      }));

      // 如果没有每日数据，生成一些默认的每日数据
      if (dailyData.length === 0 && timeRange) {
        const days = parseInt(timeRange, 10);
        if (!isNaN(days)) {
          for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dailyData.push({
              date: date.toISOString().split('T')[0],
              requests: 0,
              tokens: 0,
              cost: 0
            });
          }
          // 按日期排序
          dailyData.sort((a, b) => a.date.localeCompare(b.date));
        }
      }

      // 如果没有月度数据，生成一些默认的月度数据
      if (monthlyData.length === 0) {
        const currentYear = new Date().getFullYear();
        for (let i = 0; i < 12; i++) {
          monthlyData.push({
            month: months[i],
            year: currentYear,
            requests: 0,
            tokens: 0,
            cost: 0
          });
        }
      }

      return {
        dailyData,
        monthlyData
      };
    } catch (error) {
      this.logger.error(`Error getting overall usage: ${error}`);
      // 返回空数据
      return {
        dailyData: [],
        monthlyData: []
      };
    }
  }

  /**
   * Validate an API key
   * @param keyHash The hashed API key
   * @param keyPrefix The API key prefix
   * @returns The API key if valid, null otherwise
   */
  async validateApiKey(keyHash: string, keyPrefix: string): Promise<ApiKeyValidationResult | null> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(z.object({
          id: z.string().uuid(),
          userId: z.string().uuid(),
          status: z.string()
        }))`
          SELECT
            id,
            user_id as "userId",
            status
          FROM saito_gateway.api_keys
          WHERE key_hash = ${keyHash}
            AND key_prefix = ${keyPrefix}
            AND status = 'active'
            AND deleted_at IS NULL
        `
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error(`Error validating API key: ${error}`);
      return null;
    }
  }

  /**
   * Log API usage
   * @param params The log parameters
   */
  async logApiUsage(params: ApiUsageLogParams): Promise<string | undefined> {
    try {
      const { apiKeyId, userId, endpoint, statusCode, method = 'GET', requestSize } = params;


      // 验证必要的参数
      if (!apiKeyId) {
        return undefined;
      }

      // 如果没有提供 userId，则从 API 密钥中获取
      let userIdToUse = userId;
      if (!userIdToUse) {

        try {
          const keyResult = await this.persistentService.pgPool.query(
            sql.type(z.object({ user_id: z.string().uuid() }))`
              SELECT user_id FROM saito_gateway.api_keys WHERE id = ${apiKeyId}
            `
          );
          if (keyResult.rows.length > 0) {
            userIdToUse = keyResult.rows[0].user_id;

          } else {

            return undefined;
          }
        } catch (error) {
          this.logger.error(`[API-USAGE-LOG] Error retrieving user_id for API key ${apiKeyId}: ${error instanceof Error ? error.message : String(error)}`);
          if (error instanceof Error && error.stack) {
            this.logger.error(`[API-USAGE-LOG] Stack trace: ${error.stack}`);
          }
          return undefined;
        }
      }



      // 验证 userIdToUse 是否存在
      try {
        const userResult = await this.persistentService.pgPool.query(
          sql.type(z.object({ exists: z.boolean() }))`
            SELECT EXISTS(SELECT 1 FROM saito_gateway.users WHERE id = ${userIdToUse}) as exists
          `
        );

        if (!userResult.rows[0].exists) {
          this.logger.error(`[API-USAGE-LOG] User ID ${userIdToUse} does not exist`);
          return undefined;
        }
      } catch (error) {
        this.logger.error(`[API-USAGE-LOG] Error verifying user ID ${userIdToUse}: ${error instanceof Error ? error.message : String(error)}`);
        return undefined;
      }

      // 检查分区表是否存在，如果不存在则创建
      try {
        // 获取当前日期
        const today = new Date();
        const year = today.getFullYear();
        const month = today.getMonth() + 1;
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;

        // 计算下个月的第一天作为结束日期
        const nextMonth = month === 12 ? 1 : month + 1;
        const nextYear = month === 12 ? year + 1 : year;
        const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01`;

        const partitionName = `api_usage_${year}_${month.toString().padStart(2, '0')}`;


        // First check if the partition exists
        const checkPartitionResult = await this.persistentService.pgPool.query(sql.unsafe`
          SELECT 1
          FROM pg_class c
          JOIN pg_namespace n ON n.oid = c.relnamespace
          WHERE c.relname = ${partitionName}
          AND n.nspname = 'saito_gateway'
        `);

        // If partition doesn't exist, create it
        if (checkPartitionResult.rows.length === 0) {

          // Create the partition using a raw SQL query to avoid parameter binding issues
          // We need to use string concatenation here, but it's safe because we're not using user input
          const createPartitionSql = `
            CREATE TABLE IF NOT EXISTS saito_gateway."${partitionName}"
            PARTITION OF saito_gateway.api_usage
            FOR VALUES FROM ('${startDate}') TO ('${endDate}')
          `;

          await this.persistentService.pgPool.query(sql.unsafe([createPartitionSql]));

        } else {
          this.logger.debug(`[API-USAGE-LOG] Partition already exists: ${partitionName}`);
        }
      } catch (error) {
        this.logger.error(`[API-USAGE-LOG] Error checking/creating partition: ${error instanceof Error ? error.message : String(error)}`);
        // 继续执行，因为即使分区检查失败，插入操作可能仍然成功
      }

      // 构建插入查询
      const insertQuery = sql.type(z.object({ id: z.string().uuid() }))`
        INSERT INTO saito_gateway.api_usage (
          api_key_id,
          user_id,
          endpoint,
          method,
          status_code,
          request_size,
          date,
          timestamp
        ) VALUES (
          ${apiKeyId},
          ${userIdToUse},
          ${endpoint},
          ${method},
          ${statusCode},
          ${requestSize || null},
          CURRENT_DATE,
          NOW()
        ) RETURNING id
      `;

      this.logger.debug(`[API-USAGE-LOG] Executing insert query with values: ${JSON.stringify({
        apiKeyId,
        userIdToUse,
        endpoint,
        method,
        statusCode,
        requestSize: requestSize || null,
        date: new Date().toISOString().split('T')[0]
      })}`);

      // 插入API使用记录
      try {
        const result = await this.persistentService.pgPool.query(insertQuery);

        // 返回插入的记录ID
        if (result.rows.length > 0) {
          const insertedId = result.rows[0].id;

          return insertedId;
        }


        return undefined;
      } catch (error) {
        // 尝试获取更详细的错误信息
        this.logger.error(`[API-USAGE-LOG] Insert query failed: ${error instanceof Error ? error.message : String(error)}`);

        // 检查是否是分区表问题
        if (error instanceof Error && error.message.includes('no partition')) {
          this.logger.error(`[API-USAGE-LOG] Partition error detected. This might be due to missing partition for the current date.`);
        }

        throw error; // 重新抛出错误，让外层 catch 处理
      }
    } catch (error) {
      this.logger.error(`[API-USAGE-LOG] Error logging API usage: ${error instanceof Error ? error.message : String(error)}`);
      if (error instanceof Error && error.stack) {
        this.logger.error(`[API-USAGE-LOG] Stack trace: ${error.stack}`);
      }

      // 尝试获取更详细的数据库错误信息
      if (error instanceof Error && (error as any).code) {
        this.logger.error(`[API-USAGE-LOG] Database error code: ${(error as any).code}`);
      }

      if (error instanceof Error && (error as any).detail) {
        this.logger.error(`[API-USAGE-LOG] Database error detail: ${(error as any).detail}`);
      }

      if (error instanceof Error && (error as any).constraint) {
        this.logger.error(`[API-USAGE-LOG] Violated constraint: ${(error as any).constraint}`);
      }

      // 检查是否是分区表问题
      if (error instanceof Error && (error.message.includes('check constraint') || error.message.includes('no partition'))) {
        this.logger.error(`[API-USAGE-LOG] Constraint violation or missing partition detected. Attempting to create partition.`);

        // 尝试创建当前月份的分区表
        try {
          const today = new Date();
          const year = today.getFullYear();
          const month = today.getMonth() + 1;
          const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;

          // 计算下个月的第一天作为结束日期
          const nextMonth = month === 12 ? 1 : month + 1;
          const nextYear = month === 12 ? year + 1 : year;
          const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01`;

          const partitionName = `api_usage_${year}_${month.toString().padStart(2, '0')}`;


          // First check if the partition exists
          const checkPartitionResult = await this.persistentService.pgPool.query(sql.unsafe`
            SELECT 1
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE c.relname = ${partitionName}
            AND n.nspname = 'saito_gateway'
          `);

          // If partition doesn't exist, create it
          if (checkPartitionResult.rows.length === 0) {

            // Create the partition using a raw SQL query to avoid parameter binding issues
            // We need to use string concatenation here, but it's safe because we're not using user input
            const createPartitionSql = `
              CREATE TABLE IF NOT EXISTS saito_gateway."${partitionName}"
              PARTITION OF saito_gateway.api_usage
              FOR VALUES FROM ('${startDate}') TO ('${endDate}')
            `;

            await this.persistentService.pgPool.query(sql.unsafe([createPartitionSql]));


            // Try the insert again after creating the partition
            // We need to make sure we have all the parameters from the original request
            if (params && params.apiKeyId && params.userId && params.endpoint && params.method && params.statusCode !== undefined) {
              try {
                const retryResult = await this.persistentService.pgPool.query(sql.unsafe`
                  INSERT INTO saito_gateway.api_usage (
                    api_key_id,
                    user_id,
                    endpoint,
                    method,
                    status_code,
                    request_size,
                    date,
                    timestamp
                  ) VALUES (
                    ${params.apiKeyId},
                    ${params.userId},
                    ${params.endpoint},
                    ${params.method},
                    ${params.statusCode},
                    ${params.requestSize || null},
                    CURRENT_DATE,
                    NOW()
                  ) RETURNING id
                `);

                if (retryResult.rows.length > 0) {
                  const insertedId = retryResult.rows[0].id;
                  return insertedId;
                }
              } catch (retryError) {
                this.logger.error(`[API-USAGE-LOG] Recovery: Failed to insert record after creating partition: ${retryError instanceof Error ? retryError.message : String(retryError)}`);
              }
            } else {
              this.logger.error(`[API-USAGE-LOG] Recovery: Cannot retry insert, missing required parameters`);
            }
          } else {
            this.logger.debug(`[API-USAGE-LOG] Recovery: Partition already exists: ${partitionName}`);
          }
        } catch (partitionError) {
          this.logger.error(`[API-USAGE-LOG] Recovery: Failed to create partition: ${partitionError instanceof Error ? partitionError.message : String(partitionError)}`);
        }
      }

      return undefined;
    }
  }

  /**
   * Update API usage log
   * @param params The update parameters
   */
  async updateApiUsageLog(params: ApiUsageLogUpdateParams): Promise<void> {
    try {
      const { apiKeyId, responseTime, statusCode, responseSize } = params;
      // 验证必要的参数
      if (!apiKeyId) {
        return;
      }

      // 首先获取最近的API使用记录ID

      const recentUsageResult = await this.persistentService.pgPool.query(
        sql.type(z.object({ id: z.string().uuid() }))`
          SELECT id
          FROM saito_gateway.api_usage
          WHERE api_key_id = ${apiKeyId}
            AND date = CURRENT_DATE
          ORDER BY timestamp DESC
          LIMIT 1
        `
      );

      if (recentUsageResult.rows.length === 0) {

        return;
      }

      const usageId = recentUsageResult.rows[0].id;


      // 构建更新查询
      let updateQuery;

      if (responseSize !== undefined) {
        // 如果提供了响应大小，包含它

        updateQuery = sql.unsafe`
          UPDATE saito_gateway.api_usage
          SET
            response_time = ${responseTime},
            status_code = ${statusCode},
            response_size = ${responseSize}
          WHERE id = ${usageId}
        `;
      } else {
        // 否则只更新响应时间和状态码

        updateQuery = sql.unsafe`
          UPDATE saito_gateway.api_usage
          SET
            response_time = ${responseTime},
            status_code = ${statusCode}
          WHERE id = ${usageId}
        `;
      }

      // 执行更新

      await this.persistentService.pgPool.query(updateQuery);


      // 如果是成功的请求，记录令牌使用情况
      if (statusCode >= 200 && statusCode < 300) {


        try {
          // 获取API密钥的用户ID

          const userResult = await this.persistentService.pgPool.query(
            sql.type(z.object({ user_id: z.string().uuid() }))`
              SELECT user_id
              FROM saito_gateway.api_keys
              WHERE id = ${apiKeyId}
            `
          );

          if (userResult.rows.length === 0) {

            return;
          }

          const userId = userResult.rows[0].user_id;


          // 验证 userId 是否存在
          try {
            const userExistsResult = await this.persistentService.pgPool.query(
              sql.type(z.object({ exists: z.boolean() }))`
                SELECT EXISTS(SELECT 1 FROM saito_gateway.users WHERE id = ${userId}) as exists
              `
            );

            if (!userExistsResult.rows[0].exists) {
              this.logger.error(`[API-USAGE-UPDATE] User ID ${userId} does not exist, cannot record token usage`);
              return;
            }
          } catch (error) {
            this.logger.error(`[API-USAGE-UPDATE] Error verifying user ID ${userId}: ${error instanceof Error ? error.message : String(error)}`);
            return;
          }

          // Since we don't have model and request_size in the api_usage table,
          // use default values for token estimation
          const model = 'default-model';
          const estimatedTokens = 100; // Default token count



          // 根据模型计算成本
          // 这里使用一个简单的估算：每1000个令牌的成本
          let costPer1000Tokens = 0.002; // 默认成本

          // 根据不同模型调整成本
          if (model.includes('gpt-4')) {
            costPer1000Tokens = 0.06;
          } else if (model.includes('gpt-3.5')) {
            costPer1000Tokens = 0.002;
          } else if (model.includes('claude')) {
            costPer1000Tokens = 0.03;
          }

          const cost = (estimatedTokens / 1000) * costPer1000Tokens;


          // 估算 prompt_tokens 和 completion_tokens
          // 假设 prompt_tokens 占总 tokens 的 30%，completion_tokens 占 70%
          const promptTokens = Math.floor(estimatedTokens * 0.3);
          const completionTokens = estimatedTokens - promptTokens;



          // 构建插入查询
          const tokenInsertQuery = sql.unsafe`
            INSERT INTO saito_gateway.token_usage (
              api_key_id,
              user_id,
              model,
              prompt_tokens,
              completion_tokens,
              total_tokens,
              cost,
              date,
              timestamp
            ) VALUES (
              ${apiKeyId},
              ${userId},
              ${model},
              ${promptTokens},
              ${completionTokens},
              ${estimatedTokens},
              ${cost},
              CURRENT_DATE,
              NOW()
            )
          `;

          // 记录令牌使用情况

          await this.persistentService.pgPool.query(tokenInsertQuery);

        } catch (error) {
          this.logger.error(`[API-USAGE-UPDATE] Error recording token usage: ${error instanceof Error ? error.message : String(error)}`);
          if (error instanceof Error && error.stack) {
            this.logger.error(`[API-USAGE-UPDATE] Stack trace: ${error.stack}`);
          }

          // 尝试获取更详细的数据库错误信息
          if (error instanceof Error && (error as any).code) {
            this.logger.error(`[API-USAGE-UPDATE] Database error code: ${(error as any).code}`);
          }

          if (error instanceof Error && (error as any).detail) {
            this.logger.error(`[API-USAGE-UPDATE] Database error detail: ${(error as any).detail}`);
          }

          if (error instanceof Error && (error as any).constraint) {
            this.logger.error(`[API-USAGE-UPDATE] Violated constraint: ${(error as any).constraint}`);
          }
        }
      } else {

      }
    } catch (error) {
      this.logger.error(`[API-USAGE-UPDATE] Error updating API usage log: ${error instanceof Error ? error.message : String(error)}`);
      if (error instanceof Error && error.stack) {
        this.logger.error(`[API-USAGE-UPDATE] Stack trace: ${error.stack}`);
      }

      // 尝试获取更详细的数据库错误信息
      if (error instanceof Error && (error as any).code) {
        this.logger.error(`[API-USAGE-UPDATE] Database error code: ${(error as any).code}`);
      }

      if (error instanceof Error && (error as any).detail) {
        this.logger.error(`[API-USAGE-UPDATE] Database error detail: ${(error as any).detail}`);
      }

      if (error instanceof Error && (error as any).constraint) {
        this.logger.error(`[API-USAGE-UPDATE] Violated constraint: ${(error as any).constraint}`);
      }
    }
  }

  /**
   * Get third-party key information for decryption
   */
  async getThirdPartyKeyInfo(keyId: string): Promise<{
    keyType: string;
  } | null> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          key_type as "keyType"
        FROM saito_gateway.api_keys
        WHERE id = ${keyId}
          AND status = 'active'
          AND deleted_at IS NULL
      `);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error(`Error getting third-party key info: ${error}`);
      throw error;
    }
  }

  /**
   * Get API key by ID
   */
  async getApiKeyById(keyId: string): Promise<ApiKey | null> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          key_hash as "keyHash",
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt",
          last_used as "lastUsed",
          expiration_date as "expirationDate",
          created_by as "createdBy"
        FROM saito_gateway.api_keys
        WHERE id = ${keyId}
          AND deleted_at IS NULL
      `);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      this.logger.error(`Error getting API key by ID: ${error}`);
      throw error;
    }
  }

  /**
   * Update encrypted API key status
   */
  async updateEncryptedApiKeyStatus(
    keyId: string,
    status: 'active' | 'inactive' | 'revoked' | 'waiting-to-verify',
    userId?: string
  ): Promise<ApiKey | null> {
    try {
      let whereClause = sql.fragment`WHERE id = ${keyId} AND key_prefix = 'enc-' AND deleted_at IS NULL`;

      if (userId) {
        whereClause = sql.fragment`${whereClause} AND user_id = ${userId}`;
      }

      const result = await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.api_keys
        SET status = ${status}, updated_at = NOW()
        ${whereClause}
        RETURNING
          id,
          user_id as "userId",
          name,
          description,
          status,
          key_type as "keyType",
          provider,
          key_prefix as "keyPrefix",
          key_mask as "keyMask",
          total_requests as "totalRequests",
          total_tokens as "totalTokens",
          total_cost as "totalCost",
          created_at as "createdAt",
          updated_at as "updatedAt",
          deleted_at as "deletedAt"
      `);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0] as ApiKey;
    } catch (error) {
      this.logger.error(`Error updating encrypted API key status: ${error}`);
      throw error;
    }
  }

  /**
   * Delete encrypted API key (soft delete)
   */
  async deleteEncryptedApiKey(keyId: string, userId: string): Promise<boolean> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.api_keys
        SET deleted_at = NOW(), status = 'revoked', updated_at = NOW()
        WHERE id = ${keyId}
          AND user_id = ${userId}
          AND key_prefix = 'enc-'
          AND deleted_at IS NULL
        RETURNING id
      `);

      return result.rows.length > 0;
    } catch (error) {
      this.logger.error(`Error deleting encrypted API key: ${error}`);
      throw error;
    }
  }
}
