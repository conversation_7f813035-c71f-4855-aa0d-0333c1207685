import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ApiKeyService } from './apikey.service';
import { ApiKeyRepository } from './apikey.repository';
import { EtcdService } from './etcd.service';
import { ThirdPartyKeyService } from './third-party-key.service';
import { PersistentModule } from '@saito/persistent';

@Module({
  imports: [PersistentModule, ConfigModule],
  providers: [ApiKeyService, ApiKeyRepository, EtcdService, ThirdPartyKeyService],
  exports: [ApiKeyService, ApiKeyRepository, EtcdService, ThirdPartyKeyService],
})
export class ApiKeyModule {}
