import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Etcd3 } from 'etcd3';

export interface EtcdKeyInfo {
  key: string;
  status: 'active' | 'inactive';
  lastUsedAt: number;
}

@Injectable()
export class EtcdService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EtcdService.name);
  private etcdClient!: Etcd3;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      const etcdHost = this.configService.get<string>('ETCD_HOST', 'localhost');
      const etcdPort = this.configService.get<number>('ETCD_PORT', 2379);
      
      this.etcdClient = new Etcd3({
        hosts: [`${etcdHost}:${etcdPort}`],
      });

      // 测试连接
      await this.etcdClient.get('test').string();
      this.logger.log(`Connected to etcd at ${etcdHost}:${etcdPort}`);
    } catch (error) {
      this.logger.error('Failed to connect to etcd:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.etcdClient) {
      this.etcdClient.close();
      this.logger.log('Etcd connection closed');
    }
  }

  /**
   * 存储API key信息到etcd
   */
  async storeApiKey(keyId: string, keyInfo: EtcdKeyInfo): Promise<void> {
    try {
      const etcdKey = `/keys/openai/${keyId}`;
      const value = JSON.stringify(keyInfo);
      
      await this.etcdClient.put(etcdKey).value(value);
      this.logger.log(`Stored API key ${keyId} to etcd`);
    } catch (error) {
      this.logger.error(`Failed to store API key ${keyId} to etcd:`, error);
      throw error;
    }
  }

  /**
   * 从etcd获取API key信息
   */
  async getApiKey(keyId: string): Promise<EtcdKeyInfo | null> {
    try {
      const etcdKey = `/keys/openai/${keyId}`;
      const value = await this.etcdClient.get(etcdKey).string();
      
      if (!value) {
        return null;
      }
      
      return JSON.parse(value) as EtcdKeyInfo;
    } catch (error) {
      this.logger.error(`Failed to get API key ${keyId} from etcd:`, error);
      throw error;
    }
  }

  /**
   * 从etcd删除API key信息
   */
  async deleteApiKey(keyId: string): Promise<void> {
    try {
      const etcdKey = `/keys/openai/${keyId}`;
      await this.etcdClient.delete().key(etcdKey);
      this.logger.log(`Deleted API key ${keyId} from etcd`);
    } catch (error) {
      this.logger.error(`Failed to delete API key ${keyId} from etcd:`, error);
      throw error;
    }
  }

  /**
   * 获取所有API key
   */
  async getAllApiKeys(): Promise<{ [keyId: string]: EtcdKeyInfo }> {
    try {
      const prefix = '/keys/openai/';
      const result = await this.etcdClient.getAll().prefix(prefix);
      
      const keys: { [keyId: string]: EtcdKeyInfo } = {};
      
      for (const [key, value] of Object.entries(result)) {
        const keyId = key.replace(prefix, '');
        keys[keyId] = JSON.parse(value as string) as EtcdKeyInfo;
      }
      
      return keys;
    } catch (error) {
      this.logger.error('Failed to get all API keys from etcd:', error);
      throw error;
    }
  }

  /**
   * 更新API key的最后使用时间
   */
  async updateLastUsedAt(keyId: string): Promise<void> {
    try {
      const keyInfo = await this.getApiKey(keyId);
      if (keyInfo) {
        keyInfo.lastUsedAt = Date.now();
        await this.storeApiKey(keyId, keyInfo);
      }
    } catch (error) {
      this.logger.error(`Failed to update last used time for key ${keyId}:`, error);
      throw error;
    }
  }

  /**
   * 更新API key的状态
   */
  async updateApiKeyStatus(keyId: string, status: 'active' | 'inactive'): Promise<void> {
    try {
      const keyInfo = await this.getApiKey(keyId);
      if (keyInfo) {
        keyInfo.status = status;
        await this.storeApiKey(keyId, keyInfo);
        this.logger.log(`Updated API key ${keyId} status to ${status} in etcd`);
      } else {
        this.logger.warn(`API key ${keyId} not found in etcd, cannot update status`);
      }
    } catch (error) {
      this.logger.error(`Failed to update status for key ${keyId} in etcd:`, error);
      throw error;
    }
  }

  /**
   * 通用的get方法
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.etcdClient.get(key).string();
    } catch (error) {
      this.logger.error(`Failed to get key ${key} from etcd:`, error);
      throw error;
    }
  }

  /**
   * 通用的set方法
   */
  async set(key: string, value: string): Promise<void> {
    try {
      await this.etcdClient.put(key).value(value);
    } catch (error) {
      this.logger.error(`Failed to set key ${key} to etcd:`, error);
      throw error;
    }
  }

  /**
   * 通用的delete方法
   */
  async delete(key: string): Promise<void> {
    try {
      await this.etcdClient.delete().key(key);
    } catch (error) {
      this.logger.error(`Failed to delete key ${key} from etcd:`, error);
      throw error;
    }
  }
}
