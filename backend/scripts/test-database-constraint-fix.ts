#!/usr/bin/env tsx

/**
 * Test script to verify the database constraint fix for encrypted API keys
 */

import { NestFactory } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { ApiKeyRepository } from '../packages/libs/apikey/src/lib/apikey.repository';
import { PersistentModule } from '../packages/libs/persistent/src/index';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PersistentModule,
  ],
  providers: [ApiKeyRepository],
})
class TestModule {}

async function testDatabaseConstraintFix() {
  console.log('🧪 Testing database constraint fix for encrypted API keys...\n');

  try {
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(TestModule);
    const apiKeyRepository = app.get(ApiKeyRepository);

    const testUserId = 'test-user-001';
    const testUuid = 'test-uuid-' + Date.now();
    const testProvider = 'openai';
    const testRegion = 'asia';

    console.log('📝 Test parameters:');
    console.log(`   User ID: ${testUserId}`);
    console.log(`   UUID: ${testUuid}`);
    console.log(`   Provider: ${testProvider}`);
    console.log(`   Region: ${testRegion}`);
    console.log('');

    // Test 1: Create encrypted API key (this should now work)
    console.log('1️⃣ Testing createEncryptedApiKey...');
    try {
      const result = await apiKeyRepository.createEncryptedApiKey({
        userId: testUserId,
        uuid: testUuid,
        provider: testProvider,
        region: testRegion,
        name: 'Test Encrypted Key',
        description: 'Test encrypted API key for constraint validation',
      });

      console.log('✅ Successfully created encrypted API key:');
      console.log(`   ID: ${result.id}`);
      console.log(`   Name: ${result.name}`);
      console.log(`   Provider: ${result.providerKeyId}`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Key Type: ${result.keyType}`);
      console.log(`   KMS Key ID: ${result.kmsKeyId}`);
      console.log(`   Has Encrypted Data: ${result.encryptedKeyData ? 'Yes' : 'No'}`);
      console.log('');

      // Test 2: Verify the record exists
      console.log('2️⃣ Testing getApiKeyById...');
      const retrievedKey = await apiKeyRepository.getApiKeyById(testUuid);
      
      if (retrievedKey) {
        console.log('✅ Successfully retrieved encrypted API key:');
        console.log(`   ID: ${retrievedKey.id}`);
        console.log(`   Status: ${retrievedKey.status}`);
        console.log(`   Provider: ${retrievedKey.provider}`);
        console.log('');
      } else {
        console.log('❌ Failed to retrieve created key');
      }

      // Test 3: Update status
      console.log('3️⃣ Testing updateEncryptedApiKeyStatus...');
      const updatedKey = await apiKeyRepository.updateEncryptedApiKeyStatus(
        testUuid,
        'active',
        testUserId
      );

      if (updatedKey) {
        console.log('✅ Successfully updated encrypted API key status:');
        console.log(`   New Status: ${updatedKey.status}`);
        console.log('');
      } else {
        console.log('❌ Failed to update key status');
      }

      // Test 4: List encrypted keys
      console.log('4️⃣ Testing getEncryptedApiKeys...');
      const listResult = await apiKeyRepository.getEncryptedApiKeys(testUserId, {
        provider: testProvider,
      });

      console.log('✅ Successfully listed encrypted API keys:');
      console.log(`   Total: ${listResult.total}`);
      console.log(`   Items: ${listResult.items.length}`);
      console.log('');

      // Test 5: Clean up - delete the test key
      console.log('5️⃣ Testing deleteEncryptedApiKey...');
      const deleteResult = await apiKeyRepository.deleteEncryptedApiKey(testUuid, testUserId);
      
      if (deleteResult) {
        console.log('✅ Successfully deleted encrypted API key');
      } else {
        console.log('❌ Failed to delete key');
      }

    } catch (createError) {
      console.log('❌ Failed to create encrypted API key:');
      console.log(`   Error: ${createError instanceof Error ? createError.message : String(createError)}`);
      
      if (createError instanceof Error && createError.message.includes('constraint')) {
        console.log('\n💡 This indicates a database constraint issue.');
        console.log('   Check that the database schema supports the required fields.');
      }
    }

    await app.close();
    console.log('\n🎉 Database constraint test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run the test
testDatabaseConstraintFix().catch(console.error);
